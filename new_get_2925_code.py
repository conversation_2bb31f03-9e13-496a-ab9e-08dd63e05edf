import requests
import json

def refresh_token():
    """
    通过向 2925.com API 发送 POST 请求来刷新令牌。
    这是根据用户提供的 fetch 请求实现的。
    """
    print("=== 正在刷新 Token... ===")
    url = "https://2925.com/mailv2/auth/token"
    
    # 注意: 这里的 cookie 是身份验证的关键，需要保持有效。
    # 如果脚本无法运行，很可能是 cookie 已过期。
    headers = {
        # ":authority": "2925.com",
        # ":method": "POST",
        # ":path": "/mailv2/auth/token",
        # ":scheme": "https",

        "accept": "application/json, text/plain, */*",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9",
        "cache-control": "no-cache",
        "content-type": "application/json",
        "pragma": "no-cache",
        "priority": "u=1, i",
        "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "Content-Length":"16",
        "cookie": "returnURL=%2Fconnect%2Fauthorize%2Fcallback%3Fclient_id%3DB9257F7F9B1EF15CE%26redirect_uri%3Dhttps%253A%252F%252F2925.com%252Fauth%252Fsignin-oidc%26response_type%3Dcode%2520id_token%26scope%3Dopenid%2520profile%25202980_client%40web%2520offline_access%26response_mode%3Dform_post%26nonce%3D638844675626139171.ZGQyODcwODQtMTA0Ni00ZjNiLWEzNmItZmE0M2RlYmExMjZlZjg1OTllYWUtZjc4Ni00MDIyLTg0NzQtZjE2OGQwYjAxNTgx%26state%3DCfDJ8LvNtK7K79pCgl25c3WvKz1WJaf37cvYaQyI5-Wn2G_x8S4N7Zz8FXZkEuRdi72tsmBVNDLmWuSnlyBAukTlvSFX9tZjpTMFnBS8D30QqCp_i3Wmj9AnY4Q0iZtIL8M-QhuSidDPZoLH_ckqBXtbHSGcs8w_LzovkD7cJwbdsLykg_T7ewFK9FsWYA_ikdjNPAgBE34zOSt58AqzywoslmagWHQzFa1--eijWw7ggGAdLSM7HUUOf0iUJh1TIteR61rKJ6YH6XnCdxb1yrxWYDXNwh91IQEIHp1HsIVRhz3uqIYQB1-xlGdILAHHQEGiue22KDA4fSE3HMCqYJ3s4qJ98AFTwFmwq6KOYWyrJsKodpv6svIFLRhTYHqRHV9K8A%26x-client-SKU%3DID_NETSTANDARD2_0%26x-client-ver%3D5.5.0.0; LoginType=1; .AspNetCore.Cookies=chunks-2; .AspNetCore.CookiesC1=CfDJ8LvNtK7K79pCgl25c3WvKz1s5LkBNfGwtqeiPEP3jcy6VunFvNpEJfBatq3--OSPv1d4CLW385AgbT_tsFY7n9ImNjC6kgYeg5WhGIErthWT_MKLd4pZs2xdcyCyOnVJeZiRQm__ep0kzV7g2THFOUeK0vT-cLiaiN3iStyo16VQKqdre1EfCcWxE9MfgPpSoKg4MYV32TRqCHc-MVcC8zY-QyOawEXe2yHXQhbFVQysGYB7IjjVMlBYUYsmOGZo18QJLykwonC5c4ZcL7oWE-5eZtehTU6hX2Gp-JMtSGo9ugAbmCztWVbqqLYFp6tFoqJNYZyv36C7k_vg3wAcaNsI2r1z4BujOibOG3EoIoQpZFP4YYJK4BT59FLygXBgXGI5aANp_3HmDOpt7yLsqUulscakoGfrgttZ9ys6XAqnBcXJL90dkYvW14zSrwqxmprr9JofPSSrLAHaMn1guuy2GAZSPIVV_7yPCi1gOz1oevmuCSmhL-qKmfgBPqDH8L2SsgzQ_ZuxeBtembkN3qw2Z3_aeIMRwBnL-8SsJIp0sYyxHiPUdKrRPgbwpgcHuyGAGV3LUKfzUq8P2fuXqwq9124IlEYoxKZtDmS0Z7s9rfS_pJGjKTrNKEt8gsKJqMT__xRoyYm77AMUXuXIS7uTYssc2jBg_EVS5xQ8ctPgugxqaDZ51pBPgGdz4S1SvIxeY2sef8FW4QTdzf6iX-qI2z8LLtYmx7zIFurxfKCZ_UMPFST2txsxdphrDoktRxpVRmrfVDA7aJOJXH2zG2-NCDsktcEaEjKHdTf6ap5ykgF2CJhs26Ehnqb73RZ8S1GBlPYOnP9eMnTEEFzlEcIIs7fs6ZN7NZaSCOOORgOJ2nK-hYM49crvmFznxpb7ysu-JylrWWH3eLyVpTuVzUGJdIRtrCJaB08Ld8yE8--C95VL5ZjLuB_xsOIWJHxNo8g09vkBJv1h8yAL3oHyNPT7xbLOPCJJQAQMCcJQAnrSxKB59o5usQiTY7vj88-m1TDN1QQYrL5D4YSghaud5BCXBlmrCGlrYuhgD5-TH0dd5hPCXTN4LJTdee0AuYE8w1cunrO8Et-W7s6g3F7hzF83zb_Zpvh2gjVjPB2Hnlm4WHfxaHpU7_ZVZLK2kHFu78bUZqSOvrL_c5OxtfeP1AjwH2H8YXKfXdMziLQ30A_fkUtpPMUc2lPd2GlcRsS0IKW8QvBwK5EIg42JUc00PaolOjm5nraaQSE6lJ_gEDrfFJf_5GoKZBAawQkhO2jllLQrn6pv55qbfBjBDN-_JF63jBMHqGwXmTKapTmNKNJpgt0qLplN0RFew-zTIfEEhpH3kcWhrA2bEFY0dGgILtw-v5LEj398ZA3gVVgX7h7zVmGwU93Vaxs4Ij32hbenCFfx-TWihbp2ZcSTkCZ5XPR5nvDQhbI_k-L5swAnx8J0b9UoyatunoL9uoGoiEB--RaM8hy2pUlxyUh5D_au7TP6hBRS-8t0sB0fs7kGDZyiCQfMJzZk_E5j4HPpMGg7WOHN6zmL_00UbuQVTd8WBUafhGE09ZkCH705JdMDxRJvJaa_TX4svBX5A0ajMezonUGSLH2Fw0u6HpsmfvLY14Zn42dYhHpG2dyYkOBDI3b_lVikfn3i1yM58NiyTfW9UvOMPp47wywFBiEOkBjbvVxSlQOkA6wuZpQdq074i8zzhv3h7bfrq0XDakGmSXdwWE_H5mQsMFQtvSr3qT74d7Vd_xsywVBsr0LWfS1IAAms9s4u64gylwG-GYPiyjFG7ngRYRbDyKgg6UgQwkeeNCKVBIzeTbr7GpHqkghCF00CnYTGxTMiqiuwK3CdyOl84h2SxSj2eWm_vNvNCbp_ty_EFyybUSTkkUoAMdBsgOQZV5WNKUS_SpdnrTHtlhUylYeGQP1a2bzIFd3cGlfz0arMZdx5K9JxGPCNuXczLxDJ0AHV8t8mvtU1LI1ot40PymJgStNAuzIyzqTVjnqdsSYRXkWulcvkP4TOnOjgLd2QeJedC__80W9efZNbfqpIlX8bNty4YeV07H-GVoCFoDe1bf8poZ9092T5Q6VyKQSGBXWM7q6zZTbbs9zwAZAQZLnGVlWLnQGB5MZOj4bO5wYtY0qWuy4wTMRPqWr58BmEZty6d-H3pPR2ZDhhZo0EAgAorQPLXmHrgxVnpmuy9ChhSFBdYsHBDI2R1PhTMUIw9dbXGGJhzioxC_ULSyAZ1NubUINzdBHcbicG0T5VmxaYqoeY3IVV4R1dLqzLV9_-1QExJaYJwPP_OgBdj6GR2uMzIi5m15Do-nM1E5f6NWEnM1zzXNL74EIUJiV7yaHelnZfElTtHH6x7OanxadO04q5jHZxNvDRBta5gdHfboln6qC2QpU_E5cUcIfIeX9n4a0p3rwr6caUs4klfbMsXFkmTS1NhxuMN5sH0Y6ck9POnRN8_u7KLF2Dd3INcZCngyJMTBO6s6Lmq9aE04S5JHOkZnFdwJ-ikx5h5stYLAPLRXMwoYBUElQLK2U060YKcsOy3w9KuTdi_PPI7mQZu4P11ka621JW_d2qA3EHTzjNIGSFmqyFUwfneg1JJQjDIHeLLOLYnxOCT1xDeZdsHBXvqkcyI-tIVm2PkQE48M7reJJi6OwP9h1dBol8vvwI4MgbUlBF6Q56Xwbv7A8pFvrMSeu6ha9m6ZZZwuEhZeMiH_NC_Qf6G5Jm64WtjKPILGB61T5mbal7wJUxEPaxPSpQanOIt4ZjCtcjNgRZ2JAJi8WgbnJkyZECsOcJmNapTBnbUUS3E25-tgMIatHmHfjQLlZ694FhFTEkzrW4oVaa1wVTUnoUAG7tS3LfZB0bvI21F9TcMB5g-UFbw7xmt8z39J9WmcThleZJ69Szx7QN8BKwKfVPBORAiSksX-N51hQrSqchMA2rJShip7odcyRAuL624eC_iBMtsjHT0euYijmWfETQZ0PHUN4I7dr1lK6Bz1mMDy_vtZCka4Ry70Plj25Z1Wf6z_uQWkRtpZRoGWN7jO1gP2BSherDgeeGVgD7Oum0uBjZRIshcNA0no4Bu7Ll0Vyh8utahGZT4mk1eZfbldZcpYUEU8s1Nn5Ei33YRCFNG7_IZSJ_z1sKV75GDPKfz5fXMBkGFHwAHnZzs7vwJvdszCAQGsX20kGNhYwzK9lLz8ePUxgVqWwgRqZmcRwDI4tN0jelx914MeGE73tJNyiF18DZZ0uaso-WvdY3fygJ7XaD6DD1DMHlkzceBj4F6ZW9QZh8WmYUybEYAX3rE29LURuUS5mkG-I8Uaas526k298T0dRBFtycUhuy1MMjp-tIbs8M3ST4PDRMHdfERp2zJxYFuz6Ci-m_u7tYrF945pAj5ybbEeeE8ZZM2rOJBDwYzViNTPnjxFVemdxZ_u9TRv5Gs8qEFNgtsDR9aW1mqw4miyHrP4eY780eUTt67oMvH443Jy5SiMN2LpUTdjzOxoBXxU_xHV8ijgG29R5gAHLGsFLQbkJhSVy4fE4QX1p0UIiwBoUM0CPgp7r-obfQObtzgow-X_Or-GSIUCAcVMUfVXChvg-ejnLiZ7uKNVYcsdUqcCIn1iLPS2bZibWkY5VKnsJ9AVNE7Tr9VFhjibiy0ggxFGa-nCCbMy4fzc4UwGx52Ggmjtip6CPiuTxm3tYN5OSRSAZdMWCQekW_A9dtXtIwln9qBoRqH98bTF0kRiobbCur-Oovyw86NIbmYnOk8vXC-VgszKLP5Be_4TGIJAVV1PJN4m-PwDxW7iK3GPBKOwdYCPgXzDYGQQX-1AzEy0uvuea8--JZUtX7E9wRNPbp1nRDAHSwDPj7O1Rmajwv0esZ_-zRF7-fVVAb6LZbNb7rsIbihC1uUlSf0UVlY33zFgqWkgupGYxdnwWwIB4QtWGsp6dDoWChDBEEzXJBnmw0hfPIlSIm6SBciHcah0Y1Xlc4X9Yf_sLYfL5J3DzQxisPlTLOFcvEq; .AspNetCore.CookiesC2=DiCxRlk1QZ7x9tYOZV1eCEhVietShO_Qimb_syskmWEckskQLkGoadbqiAZ6SW9iNYfWmxLVUzEMoImFsbdtophlHOmDgag5PFZ6Kya5xgLLwlrWuApHiivs44S_UzjMo4KPW8IpMGA039cY8r8ANIebKwSN8vlWCAFjiYW2ahsTpMQQJGtyiXfg1aqCQR0dqXVY_zH-c_yAX4wHHa16y_oKcJv9bRgifLGXpIhWc9euGxPEVpJl2UIuZ0XiQ6WXgoqhHisPpUBvjhMQWWnpJOzU3qe2ogin0G25jeAdX4vTE6HK65K71k7wTcRqeAByiN8W7ITwW2A-wqXroTe-AT7wo6ZAjGoQO5Wp6piMleeJOJMgXI_YaPoz4z7LWYZoMjULYymN8e4vle2yJ9Iq0XFMx-oebPz2mUAReOmBV0sMrV9c6gIq868396fncYCZhKeObV49B71P2IAAUJscL6ubCuMZSk2-VV0l8UYo_w68Kt2ZXRn3oOw_6WXoSampsRRr7cma3-hdzEReTZqMYuM54DIijG0nqtYKZY2KqwCqkwp2NmtHw4s4Tw; auc=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yuTRgeGUWoKBIRpugC48XZ37vi8gBzCu_G8DrD8HY3Q; aut=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.i3wb3wf41mhqcDEWFsz-Qov6r73UyHhjgvYgYFaEcA8; account=yunfengemail%402925.com; nickname=yunfengemail; uid=ae8d5068-0d04-3a32-9a27-2fff212907b3; ano=undefined; jwt_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Q0GFI17ONAZRxopLWMfX9j6a8uyBrOcfIvi4aA1HKFw",
        "Referer": "https://2925.com/",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }
    
    body = {}

    try:
        response = requests.post(url, headers=headers, json=body)
        print(response.text)
        response.raise_for_status()  # Raise an exception for bad status codes
        
        response_data = response.json()
        
        print(f"Token 刷新成功, 响应代码: {response_data.get('code')}")
        token = response_data.get("result")
        print(f"获取到新的 Token (前30位): {token[:30]}...")
        
        # 返回新的 token 和用于请求的 cookie
        return token, headers['cookie']
        
    except requests.exceptions.RequestException as e:
        print(f"刷新 Token 请求过程中发生错误: {e}")
        return None, None

def read_mail_content(token, cookie):
    """读取邮件内容API"""
    print("=== 正在读取邮件内容... ===")
    
    url = "https://2925.com/mailv2/maildata/MailRead/mails/read"
    
    params = {
        "MessageID": "8708ecd4f768432984e8cd5cf729f175",
        "FolderName": "Inbox", 
        "MailBox": "<EMAIL>",
        "IsPre": "false",
        "traceId": "f02d5f3aa5ef"
    }
    
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "authorization": f"Bearer {token}",
        "cache-control": "no-cache",
        "deviceuid": "5fce6dfe-5898-4ec4-a30e-9eadd7ef754f",
        "pragma": "no-cache",
        "priority": "u=1, i",
        "sec-ch-ua": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors", 
        "sec-fetch-site": "same-origin",
        "x-requested-with": "XMLHttpRequest",
        "Referer": "https://2925.com/",
        "cookie": cookie,
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }
    
    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()
        print(f"读取邮件内容成功, 状态码: {response.status_code}")
        # print(f"响应内容: {response.text}")
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"读取邮件内容请求出错: {e}")
        return None

def get_mail_list(token, cookie):
    """获取邮件列表API"""
    print("=== 正在获取邮件列表... ===")
    
    url = "https://2925.com/mailv2/maildata/MailList/mails"
    
    params = {
        "Folder": "Inbox",
        "MailBox": "<EMAIL>", 
        "FilterType": "0",
        "PageIndex": "1",
        "PageCount": "25",
        "traceId": "139e22e0f77c"
    }
    
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "authorization": f"Bearer {token}",
        "cache-control": "no-cache",
        "deviceuid": "5fce6dfe-5898-4ec4-a30e-9eadd7ef754f",
        "pragma": "no-cache",
        "priority": "u=1, i",
        "sec-ch-ua": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin", 
        "x-requested-with": "XMLHttpRequest",
        "Referer": "https://2925.com/",
        "cookie": cookie,
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }
    
    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()
        print(f"获取邮件列表成功, 状态码: {response.status_code}")
        # print(f"响应内容: {response.text}")
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"获取邮件列表请求出错: {e}")
        return None

def main():
    """主方法"""
    print("开始执行2925邮箱API请求...\n")
    
    token, cookie = refresh_token()
    
    if token and cookie:
        print("\n" + "="*50 + "\n")
        read_mail_content(token, cookie)
        
        print("\n" + "="*50 + "\n")
        get_mail_list(token, cookie)
        
        print(f"\n程序执行完成！")
    else:
        print("\n获取 Token 失败，无法继续执行。")
    

if __name__ == "__main__":
    main() 




# fetch("https://2925.com/mailv2/auth/token", {
#   "headers": {
#     "accept": "application/json, text/plain, */*",
#     "accept-language": "zh-CN,zh;q=0.9",
#     "cache-control": "no-cache",
#     "content-type": "application/json",
#     "pragma": "no-cache",
#     "priority": "u=1, i",
#     "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
#     "sec-ch-ua-mobile": "?0",
#     "sec-ch-ua-platform": "\"macOS\"",
#     "sec-fetch-dest": "empty",
#     "sec-fetch-mode": "cors",
#     "sec-fetch-site": "same-origin"
#   },
#   "referrer": "https://2925.com/",
#   "referrerPolicy": "strict-origin-when-cross-origin",
#   "body": "{\"timeout\":5000}",
#   "method": "POST",
#   "mode": "cors",
#   "credentials": "include"
# });