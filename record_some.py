import os
import sys
import json
import uuid
import hashlib
import shutil
import sqlite3
import platform
import re
import tempfile
import glob
from datetime import datetime
import configparser

# 定义emoji常量
EMOJI = {
    "FILE": "📄",
    "BACKUP": "💾",
    "SUCCESS": "✅",
    "ERROR": "❌",
    "INFO": "ℹ️",
    "RESET": "🔄",
    "WARNING": "⚠️",
}

# SQLite数据库文件路径
DB_FILE = "cursor_machine_ids.db"

def setup_database():
    """检查数据库是否存在，不存在则创建"""
    # 创建数据库连接
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    # 检查表是否存在，不存在则创建
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='machine_ids'")
    table_exists = cursor.fetchone() is not None
    
    if not table_exists:
        # 如果表不存在，创建新表
        cursor.execute('''
        CREATE TABLE machine_ids (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT,
            machine_id TEXT,                    /* machineId */
            telemetry_machine_id TEXT,          /* telemetry.machineId */
            env_machine_id TEXT,                /* environment.machineId */
            anonymized_id TEXT,                 /* anonymizedId */
            session_id TEXT,                    /* sessionId */
            agent_id TEXT,                      /* agentId */
            telemetry_anon_id TEXT,             /* telemetry.anon_id */
            telemetry_machine_id_hash TEXT,     /* telemetry.machineId_hash */
            telemetry_session_id TEXT,          /* telemetry.session_id */
            mac_machine_id TEXT,                /* telemetry.macMachineId */
            installation_id TEXT,               /* telemetry.installation_id */
            storage_service_machine_id TEXT,    /* storage.serviceMachineId */
            telemetry_dev_device_id TEXT,       /* telemetry.devDeviceId */
            telemetry_sqm_id TEXT               /* telemetry.sqmId */
        )
        ''')
        print(f"{EMOJI['INFO']} 已创建新数据库表")
    
    conn.commit()
    return conn

def get_user_documents_path():
    """获取用户文档文件夹路径"""
    if sys.platform == "win32":
        try:
            import winreg
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Shell Folders") as key:
                documents_path, _ = winreg.QueryValueEx(key, "Personal")
                return documents_path
        except Exception as e:
            # 备选方案
            return os.path.join(os.path.expanduser("~"), "Documents")
    elif sys.platform == "darwin":
        return os.path.join(os.path.expanduser("~"), "Documents")
    else:  # Linux
        # 获取实际用户的主目录
        sudo_user = os.environ.get('SUDO_USER')
        if sudo_user:
            return os.path.join("/home", sudo_user, "Documents")
        return os.path.join(os.path.expanduser("~"), "Documents")

def get_cursor_paths():
    """获取Cursor相关路径"""
    system = platform.system()
    
    # 读取配置文件
    config = configparser.ConfigParser()
    config_dir = os.path.join(get_user_documents_path(), ".cursor-free-vip")
    config_file = os.path.join(config_dir, "config.ini")
    
    # 创建配置目录(如果不存在)
    if not os.path.exists(config_dir):
        os.makedirs(config_dir)
    
    # 不同系统的默认路径
    default_paths = {
        "Darwin": "/Applications/Cursor.app/Contents/Resources/app",
        "Windows": os.path.join(os.getenv("LOCALAPPDATA", ""), "Programs", "Cursor", "resources", "app"),
        "Linux": ["/opt/Cursor/resources/app", "/usr/share/cursor/resources/app", 
                 os.path.expanduser("~/.local/share/cursor/resources/app"), "/usr/lib/cursor/app/"]
    }
    
    # 如果配置不存在，创建默认配置
    if not os.path.exists(config_file):
        for section in ['MacPaths', 'WindowsPaths', 'LinuxPaths']:
            if not config.has_section(section):
                config.add_section(section)
        
        if system == "Darwin":
            config.set('MacPaths', 'cursor_path', default_paths["Darwin"])
        elif system == "Windows":
            config.set('WindowsPaths', 'cursor_path', default_paths["Windows"])
        elif system == "Linux":
            # 对于Linux，尝试找到第一个存在的路径
            for path in default_paths["Linux"]:
                if os.path.exists(path):
                    config.set('LinuxPaths', 'cursor_path', path)
                    break
            else:
                # 如果没有找到路径，使用第一个作为默认值
                config.set('LinuxPaths', 'cursor_path', default_paths["Linux"][0])
        
        with open(config_file, 'w', encoding='utf-8') as f:
            config.write(f)
    else:
        config.read(config_file, encoding='utf-8')
    
    # 根据系统获取路径
    if system == "Darwin":
        section = 'MacPaths'
    elif system == "Windows":
        section = 'WindowsPaths'
    elif system == "Linux":
        section = 'LinuxPaths'
    else:
        raise OSError(f"不支持的操作系统: {system}")
    
    if not config.has_section(section) or not config.has_option(section, 'cursor_path'):
        raise OSError("未配置 Cursor 路径")
    
    base_path = config.get(section, 'cursor_path')
    
    # 对于Linux，如果配置的路径不存在，尝试查找第一个存在的路径
    if system == "Linux" and not os.path.exists(base_path):
        for path in default_paths["Linux"]:
            if os.path.exists(path):
                base_path = path
                # 更新配置
                config.set(section, 'cursor_path', path)
                with open(config_file, 'w', encoding='utf-8') as f:
                    config.write(f)
                break
    
    if not os.path.exists(base_path):
        raise OSError(f"找不到 Cursor 路径: {base_path}")
    
    pkg_path = os.path.join(base_path, "package.json")
    main_path = os.path.join(base_path, "out/main.js")
    
    # 检查文件是否存在
    if not os.path.exists(pkg_path):
        raise OSError(f"找不到 package.json: {pkg_path}")
    if not os.path.exists(main_path):
        raise OSError(f"找不到 main.js: {main_path}")
    
    return (pkg_path, main_path)

def get_cursor_machine_id_path():
    """获取Cursor machineId文件路径"""
    # 读取配置
    config_dir = os.path.join(get_user_documents_path(), ".cursor-free-vip")
    config_file = os.path.join(config_dir, "config.ini")
    config = configparser.ConfigParser()
    
    if os.path.exists(config_file):
        config.read(config_file)
    
    if sys.platform == "win32":  # Windows
        if not config.has_section('WindowsPaths'):
            config.add_section('WindowsPaths')
            config.set('WindowsPaths', 'machine_id_path', 
                os.path.join(os.getenv("APPDATA"), "Cursor", "machineId"))
        return config.get('WindowsPaths', 'machine_id_path')
        
    elif sys.platform == "linux":  # Linux
        if not config.has_section('LinuxPaths'):
            config.add_section('LinuxPaths')
            config.set('LinuxPaths', 'machine_id_path',
                os.path.expanduser("~/.config/cursor/machineid"))
        return config.get('LinuxPaths', 'machine_id_path')
        
    elif sys.platform == "darwin":  # macOS
        if not config.has_section('MacPaths'):
            config.add_section('MacPaths')
            config.set('MacPaths', 'machine_id_path',
                os.path.expanduser("~/Library/Application Support/Cursor/machineId"))
        return config.get('MacPaths', 'machine_id_path')
        
    else:
        raise OSError(f"不支持的操作系统: {sys.platform}")

class MachineIDResetter:
    def __init__(self):
        """初始化重置器"""
        try:
            self.state_dir = os.path.join(os.path.expanduser("~"), ".cursor")
            self.db_path = os.path.join(self.state_dir, "cursor-app.json")
            if not os.path.exists(self.state_dir):
                os.makedirs(self.state_dir, exist_ok=True)
            
            print(f"{EMOJI['INFO']} 初始化重置器成功")
            
        except Exception as e:
            print(f"{EMOJI['ERROR']} 初始化失败: {str(e)}")
            raise

    def generate_new_ids(self):
        """生成新的ID，按照指定的格式和长度"""
        try:
            # 生成服务机器ID (同时用于devDeviceId和serviceMachineId)
            service_machine_id = str(uuid.uuid4())
            
            # 生成新的UUID和ID
            new_ids = {
                "machineId": str(uuid.uuid4()),
                "environment.machineId": str(uuid.uuid4()),
                "anonymizedId": hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest(),
                "sessionId": str(uuid.uuid4()),
                "agentId": str(uuid.uuid4()),
                "telemetry.anon_id": str(uuid.uuid4()),
                "telemetry.session_id": str(uuid.uuid4()),
                "telemetry.installation_id": str(uuid.uuid4()),
                
                # 添加特定格式的ID
                "storage.serviceMachineId": service_machine_id,
                "telemetry.devDeviceId": service_machine_id,  # 与serviceMachineId相同
                "telemetry.sqmId": "{" + str(uuid.uuid4()).upper() + "}",
                "telemetry.machineId": hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest(),
                "telemetry.machineId_hash": hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest(),
                "telemetry.macMachineId": hashlib.sha256((str(uuid.uuid4()) + str(uuid.uuid4())).encode()).hexdigest() + hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()
            }
            print(f"{EMOJI['SUCCESS']} 成功生成新的ID")
            return new_ids
        except Exception as e:
            print(f"{EMOJI['ERROR']} 生成ID失败: {str(e)}")
            raise

    def update_sqlite_db(self, new_ids, conn):
        """将新生成的ID存入SQLite数据库"""
        try:
            cursor = conn.cursor()
            
            # 插入新记录，确保所有字段都被正确存储
            cursor.execute('''
            INSERT INTO machine_ids (
                timestamp,
                machine_id,
                telemetry_machine_id,
                env_machine_id,
                anonymized_id,
                session_id,
                agent_id,
                telemetry_anon_id,
                telemetry_machine_id_hash,
                telemetry_session_id,
                mac_machine_id,
                installation_id,
                storage_service_machine_id,
                telemetry_dev_device_id,
                telemetry_sqm_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                new_ids.get("machineId", ""),
                new_ids.get("telemetry.machineId", ""),
                new_ids.get("environment.machineId", ""),
                new_ids.get("anonymizedId", ""),
                new_ids.get("sessionId", ""),
                new_ids.get("agentId", ""),
                new_ids.get("telemetry.anon_id", ""),
                new_ids.get("telemetry.machineId_hash", ""),
                new_ids.get("telemetry.session_id", ""),
                new_ids.get("telemetry.macMachineId", ""),
                new_ids.get("telemetry.installation_id", ""),
                new_ids.get("storage.serviceMachineId", ""),
                new_ids.get("telemetry.devDeviceId", ""),
                new_ids.get("telemetry.sqmId", "")
            ))
            
            conn.commit()
            print(f"{EMOJI['SUCCESS']} 成功将ID存入数据库")
            
        except Exception as e:
            print(f"{EMOJI['ERROR']} 数据库更新失败: {str(e)}")
            conn.rollback()
            raise

    def update_system_ids(self, new_ids):
        """更新系统ID"""
        system = platform.system()
        try:
            if system == "Windows":
                self._update_windows_machine_guid()
                self._update_windows_machine_id()
                print(f"{EMOJI['SUCCESS']} 成功更新系统ID")
            elif system == "Darwin":  # macOS
                self._update_macos_platform_uuid(new_ids)
                print(f"{EMOJI['SUCCESS']} 成功更新系统ID")
            else:
                print(f"{EMOJI['INFO']} 系统 {system} 不需要更新系统ID")
        except Exception as e:
            print(f"{EMOJI['ERROR']} 更新系统ID失败: {str(e)}")
            raise

    def _update_windows_machine_guid(self):
        """更新Windows MachineGuid"""
        try:
            import winreg
            key = winreg.OpenKey(
                winreg.HKEY_LOCAL_MACHINE,
                "SOFTWARE\\Microsoft\\Cryptography",
                0,
                winreg.KEY_WRITE | winreg.KEY_WOW64_64KEY
            )
            new_guid = str(uuid.uuid4())
            winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, new_guid)
            winreg.CloseKey(key)
            print(f"{EMOJI['SUCCESS']} Windows MachineGuid 已更新")
        except PermissionError as e:
            print(f"{EMOJI['ERROR']} 权限不足: {str(e)}")
            raise
        except Exception as e:
            print(f"{EMOJI['ERROR']} 更新Windows MachineGuid失败: {str(e)}")
            raise
    
    def _update_windows_machine_id(self):
        """更新Windows MachineId在SQMClient注册表中"""
        try:
            import winreg
            # 1. 生成新的GUID
            new_guid = "{" + str(uuid.uuid4()).upper() + "}"
            print(f"{EMOJI['INFO']} 新的MachineId: {new_guid}")
            
            # 2. 打开注册表项
            try:
                key = winreg.OpenKey(
                    winreg.HKEY_LOCAL_MACHINE,
                    r"SOFTWARE\Microsoft\SQMClient",
                    0,
                    winreg.KEY_WRITE | winreg.KEY_WOW64_64KEY
                )
            except FileNotFoundError:
                # 如果注册表项不存在，创建它
                key = winreg.CreateKey(
                    winreg.HKEY_LOCAL_MACHINE,
                    r"SOFTWARE\Microsoft\SQMClient"
                )
            
            # 3. 设置MachineId值
            winreg.SetValueEx(key, "MachineId", 0, winreg.REG_SZ, new_guid)
            winreg.CloseKey(key)
            
            print(f"{EMOJI['SUCCESS']} Windows MachineId 已更新")
            return True
            
        except PermissionError:
            print(f"{EMOJI['ERROR']} 权限不足")
            print(f"{EMOJI['WARNING']} 请以管理员身份运行")
            return False
        except Exception as e:
            print(f"{EMOJI['ERROR']} 更新Windows MachineId失败: {str(e)}")
            return False
    
    def _update_macos_platform_uuid(self, new_ids):
        """更新macOS平台UUID"""
        try:
            uuid_file = "/var/root/Library/Preferences/SystemConfiguration/com.apple.platform.uuid.plist"
            if os.path.exists(uuid_file):
                # 使用sudo执行plutil命令
                cmd = f'sudo plutil -replace "UUID" -string "{new_ids["telemetry.macMachineId"]}" "{uuid_file}"'
                result = os.system(cmd)
                if result == 0:
                    print(f"{EMOJI['SUCCESS']} macOS平台UUID已更新")
                else:
                    raise Exception(f"{EMOJI['ERROR']} 执行plutil命令失败")
        except Exception as e:
            print(f"{EMOJI['ERROR']} 更新macOS平台UUID失败: {str(e)}")
            raise

    def update_machine_id_file(self, machine_id):
        """
        更新machineId文件
        Args:
            machine_id (str): 新的机器ID
        Returns:
            bool: 成功返回True，否则返回False
        """
        try:
            # 获取machineId文件路径
            machine_id_path = get_cursor_machine_id_path()
            
            # 如果目录不存在则创建
            os.makedirs(os.path.dirname(machine_id_path), exist_ok=True)

            # 如果文件存在则创建备份
            if os.path.exists(machine_id_path):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"{machine_id_path}.backup.{timestamp}"
                try:
                    shutil.copy2(machine_id_path, backup_path)
                    print(f"{EMOJI['INFO']} 备份已创建: {backup_path}")
                except Exception as e:
                    print(f"{EMOJI['INFO']} 无法创建备份: {str(e)}")

            # 将新的机器ID写入文件
            with open(machine_id_path, "w", encoding="utf-8") as f:
                f.write(machine_id)

            print(f"{EMOJI['SUCCESS']} 成功更新machineId文件")
            return True

        except Exception as e:
            print(f"{EMOJI['ERROR']} 更新machineId文件失败: {str(e)}")
            return False

    def reset_machine_ids(self, conn):
        """重置机器ID并备份原始文件"""
        try:
            print(f"{EMOJI['INFO']} 检查中...")

            if os.path.exists(self.db_path):
                with open(self.db_path, "r", encoding="utf-8") as f:
                    config = json.load(f)
                
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"{self.db_path}.bak.{timestamp}"
                print(f"{EMOJI['BACKUP']} 创建备份: {backup_path}")
                shutil.copy2(self.db_path, backup_path)
            else:
                config = {}

            print(f"{EMOJI['RESET']} 生成新ID...")
            new_ids = self.generate_new_ids()

            # 更新配置文件
            config.update(new_ids)

            # 保存到JSON文件
            print(f"{EMOJI['FILE']} 保存JSON...")
            with open(self.db_path, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=4)

            # 更新SQLite数据库
            self.update_sqlite_db(new_ids, conn)

            # 更新系统ID
            self.update_system_ids(new_ids)

            # 更新machineId文件
            self.update_machine_id_file(new_ids["machineId"])

            print(f"{EMOJI['SUCCESS']} 重置成功")
            print(f"\n{EMOJI['INFO']} 新ID:")
            for key, value in new_ids.items():
                print(f"{EMOJI['INFO']} {key}: {value}")

            return True

        except PermissionError as e:
            print(f"{EMOJI['ERROR']} 权限错误: {str(e)}")
            print(f"{EMOJI['INFO']} 请以管理员身份运行")
            return False
        except Exception as e:
            print(f"{EMOJI['ERROR']} 处理错误: {str(e)}")
            return False

def list_saved_ids():
    """列出已保存的所有机器ID"""
    try:
        # 调用setup_database确保表已创建
        conn = setup_database()
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT * FROM machine_ids
        ORDER BY timestamp DESC
        ''')
        
        records = cursor.fetchall()
        
        if not records:
            print(f"{EMOJI['INFO']} 数据库中没有保存的机器ID")
            return
        
        print(f"\n{EMOJI['INFO']} 已保存的机器ID:")
        print("-" * 120)
        print(f"{'ID':<3} {'时间戳':<22} {'机器ID':<36} {'遥测机器ID':<36}")
        print("-" * 120)
        
        for record in records:
            rid = record[0]
            timestamp = record[1]
            machine_id = record[2]
            telemetry_machine_id = record[3]
            print(f"{rid:<3} {timestamp:<22} {machine_id:<36} {telemetry_machine_id:<36}")
        
        print("\n要查看详细信息，请输入记录ID，或按Enter返回:")
        choice = input(f"{EMOJI['INFO']} 请输入记录ID: ")
        
        if choice.isdigit():
            rid = int(choice)
            for record in records:
                if record[0] == rid:
                    print(f"\n{EMOJI['INFO']} 记录 {rid} 详细信息:")
                    print("-" * 100)
                    fields = [
                        "ID", "时间戳", "机器ID", "遥测机器ID", "环境机器ID", 
                        "匿名ID", "会话ID", "代理ID", "遥测匿名ID", 
                        "遥测机器ID哈希", "遥测会话ID", "Mac机器ID", "安装ID"
                    ]
                    for i, field in enumerate(fields):
                        if i < len(record):
                            print(f"{field}: {record[i]}")
                    break
            else:
                print(f"{EMOJI['ERROR']} 未找到ID为 {rid} 的记录")
        
        conn.close()
        
    except Exception as e:
        print(f"{EMOJI['ERROR']} 列出保存的ID时出错: {str(e)}")

def reset_cursor_machine_id():
    """重置Cursor机器ID并保存到数据库"""
    try:
        # 设置数据库
        conn = setup_database()
        
        # 创建重置器并执行重置
        resetter = MachineIDResetter()
        success = resetter.reset_machine_ids(conn)
        
        # 关闭数据库连接
        conn.close()
        
        if success:
            print(f"{EMOJI['SUCCESS']} Cursor机器ID已成功重置并保存到数据库")
        else:
            print(f"{EMOJI['ERROR']} 重置Cursor机器ID失败")
        
        return success
    except Exception as e:
        print(f"{EMOJI['ERROR']} 发生错误: {str(e)}")
        return False

def main():
    """主函数 - 直接执行重置操作并保存数据"""
    print(f"\n{'='*50}")
    print(f"{EMOJI['RESET']} Cursor机器ID重置工具")
    print(f"{'='*50}")
    
    print(f"\n{EMOJI['INFO']} 正在自动执行Cursor机器ID重置...")
    
    try:
        # 执行重置操作
        success = reset_cursor_machine_id()
        
        if success:
            print(f"\n{EMOJI['SUCCESS']} 操作完成：Cursor机器ID已成功重置并保存到数据库")
            
            # 显示最近保存的记录
            print(f"\n{EMOJI['INFO']} 显示最近保存的记录:")
            conn = setup_database()
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT * FROM machine_ids 
            ORDER BY id DESC LIMIT 1
            ''')
            
            record = cursor.fetchone()
            
            if record:
                fields = [
                    "ID", "时间戳", "机器ID", "遥测机器ID", "环境机器ID", 
                    "匿名ID", "会话ID", "代理ID", "遥测匿名ID", 
                    "遥测机器ID哈希", "遥测会话ID", "Mac机器ID", "安装ID"
                ]
                print("-" * 100)
                for i, field in enumerate(fields):
                    if i < len(record):
                        print(f"{field}: {record[i]}")
                print("-" * 100)
            
            conn.close()
        else:
            print(f"\n{EMOJI['ERROR']} 操作失败：Cursor机器ID重置过程中出现错误")
    except Exception as e:
        print(f"\n{EMOJI['ERROR']} 执行过程中出现错误: {str(e)}")
    
    print(f"\n{EMOJI['INFO']} 程序执行完毕。")

if __name__ == "__main__":
    main()
