#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能邮箱验证码获取系统
根据邮箱地址自动判断并选择对应的验证码获取方式
"""

import re
import logging
import time
import requests
import json
from typing import Dict, Optional, Tuple
from get_mail_code import get_2_code
import poplib
import email

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


# 设置 device_uid   jwt_token   cookie    在网站上
class SmartEmailVerification:
    """智能邮箱验证码获取器"""
    
    def __init__(self):
        """初始化邮箱路由配置"""
        # 2925邮箱API基础配置模板
        self.api_2925_base_config = {
            'token_url': 'https://www.2925.com/mailv2/auth/token',
            'mail_list_url': 'https://2925.com/mailv2/maildata/MailList/mails',
            'mail_read_url': 'https://2925.com/mailv2/maildata/MailRead/mails/read',
            'jwt_token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.qB1HqxHcK-rdMsU6lKrzfdBtIPBIhZy3XdTq9l9xizk',
            'device_uid': '71fc87a4-7e6b-43a0-8941-cc6e51c4a5cc',  # 可选，为空时使用基础配置
            "cookie": "returnURL=%2Fconnect%2Fauthorize%2Fcallback%3Fclient_id%3DB9257F7F9B1EF15CE%26redirect_uri%3Dhttps%253A%252F%252Fwww.2925.com%252Fauth%252Fsignin-oidc%26response_type%3Dcode%2520id_token%26scope%3Dopenid%2520profile%25202980_client%40web%2520offline_access%26response_mode%3Dform_post%26nonce%3D638838645191247299.NTQzY2NlZTUtODgyYi00NDg2LWE0ZWUtYjE4ZmVlODVkODEzYWVmYWYxOWEtYmNiNy00MGM2LThkMDQtYjRlNzEzZDgzMmZh%26state%3DCfDJ8LvNtK7K79pCgl25c3WvKz1yDKEto8Lm1_YqdifONsH4a6vr55y9tYlCvUV8erOVHoyz0qSl86h98G_1PpUuBXpYfcQvsUuZ887KQyz9HuKz7R-xiapQc-Drxq9YnqWyy9m9aCyem9BkWl0tdAnqh_qwZtd-gMAxFzsu9G7MKyBGPXTXnk1HBeTXqoyNcvkpJFGHY5MHUvaY5J_fDtssBkLKoWpq82Y5GnjRtVCEudHqzses4fxrXGzWbp8LNqh6TD1a5KH_mTll4zAbKbBBzvEFEQ8FTE1RxS9KFjqhcuNCSl1qOWW-Glp_N2WGYpRsZvHqw2ra6BMlGqnWbnaylIp2kBov3o2RjD8hZm2_lttaYOwOawaRAYKrvkQpMO-eoikAVV5OMW089Hi0EuOlM-w%26x-client-SKU%3DID_NETSTANDARD2_0%26x-client-ver%3D5.5.0.0; LoginType=1; .AspNetCore.Cookies=chunks-2; .AspNetCore.CookiesC1=CfDJ8LvNtK7K79pCgl25c3WvKz1fkqwLoJSuq62L_UmkEVCniSLRAbGTsdkrpNEhVgZG34mQ8kfFeUu7KDXt_hPAfoRJ4sHq72bAGflHCgZlWlVHJDXg_54p3C5wXfsO0BFaW-9gRsOUEMgS0ZdUhsFrJ1tvAZdsBlgL4D8LpwZcaprCqP--zefWypcaJlAZlkAdxn7Bk1ETrTAX8uP7fSBm8hqTDDjc-BqQ6SdFdXgjcf3tFVAU-hRYCZNtRyLoCdesy_OKFt0yWWc1O8C69-Yf9xWDHgst3K5FcnDWSRdwrqeKCXNkaSw7JHXzptUtNIYMdb-09ONm-mHK73FmdOVJwwhOJI0bCV03K18AqEZ9J3xsfgUrnanH73Na9aauBUuYroJ-w-zhyNCi-TwQlaotW0ztpbkBTW1z1hDnPz7OXTndzDGeAWXBdJ4k6ZCpo36yHnsJ_uYSjLQg4U5gMuzfXZIBB4gmxqqWdPXS2aZl942oMTehI5b-9gMRsuxU05itqr9WzmIHZ2-U_T-aNG9N-ZOWVRsii0CGF5k3RHOVWfSHdSpgc1Du3LCbOTvD0OGd6yfQQeNPKYhMZYgvNcYzzDb0LkRMgp_wHMI7fNgGNA0gIe99QAYjbMVlvIc7GcvzcrEdRAi4TREsnU_eKkYkMhDw4YAIQ7MAhDG3Cp377U_v2a2B0iXRTrhnL07bLYmVPcKbVexLeXg3i_qtusRco-EVtWO5k60lvKbsxQO43asXT_vF5IQvHPGYCicwg40AoycjfD8nRX2mi9aRjcd5Ztnq0rEoEEcy_i1V2orpEzY_fv78o8kUIfitQrQlTLR-cvLVaP5_awAUSD9-CR59xqpwbgdn92_gIx8xh7batV8FV-jU6rA5ngh0K5NKY4VgLgR_-Jn89aIPV7x4kSPDUOb_7N264G_XKqiaiyi-SDTuUISCoUNq-78rbroriXRJg8b2BwAe1fuC12uXRfvbh9B2jIRNZssOITcShl5vDgfthNQrSwC4zYhrepcC0ZXrbzdb-yQlCPTVpxcDfsO4QNXyYKCclOx5OQCbR3i2DbFvJT_9YvNwyzxTu9XY1eQLz3G3zeHzJbH4X7UspHqFZ14b4_NsWS5pexhQ9QXJyaSxAsTah6H-fcPTILmCvoZjQ6ipgctAogTK2OIi50Jh3fuHbNKjsVlQGNXw8A0RDw0hgnNCpr-vycobGdMM7JQattHElPYzzHHo3sdRjLsPeosFXydCZ4Akmr5Mdpo89WTkjMFuB6B8WzjZQ_FNu4a__F_HIeBbhEzoHvEKDiyyX5DAPXF16ot00VBDVMGAu_0YATrJO8zbYAOY11Tl1JenCAI0KDbVBrXYxD7JDTgTEid4eaDZ_Wa3swGlGQSMd8VyTVqP2JxQQXYKQ3cRui70BPLWaz5T11S76Rb1uaO3shHjto36_MF6JWOTTVsQFGE8txZicor9iHB1NSzYtkEmTaLWRBSdF1QQ_7z30frx1XQaYqTfmpr7apRkNTWdDM_VDJZXfVhvAB6amzE-wGLc1p1FLgmuMxLRGEJ2r_QximEeKP0IZQMuyC5L8ePwMVqdglRr8T2v3Sf99BjBDNdRnpJvXm3Y-PSg_nmW8ffQI9-De9ysl2gb8C3u8dgKoVjtGxjqx7OC4ZlUgeSngPjNkMR2aso_kLQkwHhqqFyojo7T_hBIlxhdXq-COc-wVTtvibyyI98wBZ0FrJwCC0z3QnQb4_-nhmklUmC17HvOnl5SgJ6_K1zTVhsDpXHyJbZL5IbSeM-C_VnyqJzeFOSblVlvOBYBwvq3Hb8tbvGXw00ET4YN3igZ7FzKgj2sLM4Sjm5T5AvbmkOu8XpqyIAAckpnoysWBfNxTsd6JVubWcLIHUgfvEgzX1AjQg5lQR9-OVolB8NkCyVKRSZFNiR4wxBOoBsIPq3fho8o5uYuYt0hFKkS0ukgkPZqRPAI7yxR5-VMkf3T-wk5uurSDp66roYrW5ZgfC4v0ENoKKXpc86heDY-RIVYtQBu1D7-5T7VraQrYZ9E0MNb_J8LcXAVRyaiip78CGrhbl4vEgkoGoBFcM2q85Ce2LgQT4MlVtUOFpOMCQaUjYQO4atWEv5sD4x2yxFIQ5Pg_gkFzKfiWzWuNveIaGuwbIVaQ0e37B6FTwpRUIdc8IZ_Vxt5IeG0KB97qQ46BHHXzl-R6IYKrynP4M9h8pIGU23L6pIpry-XfkoxBfoWi2-N_2WjA26xbC7JLARxS7CaBEAyNuawhO160vOWKn6UOjaaBy1sfVCE_YzznhOTc6eeH1fI1Hdh-5u1SHJxKFznlprUW2OFfN6dpw2Bgr7gU30oXlbJnGwXy8GIY1b81Q1upTZ7gNbt_tbEbG4ql-YK-9Mec_1TlRetQhqgcn1XAbFyGi-a9M-wE4lBGlf_WcMqSt8o8_HKGz0PiIEmOcHPFNE1w1HSB3QIAbztzA0Dtw4Q9xeY-YYWC1vuNaTEOytIlKH6A7nBSN27PTU1dSucwdt_G2VImCC4gDv5pA3j6_JvHJ5z6htA9mY3KnoR4kJmll-X9RQToArSAE2ovmGbA-TXgdOb_YDUt2gjcefistSvSumf6I8w1DoZSkex5HmpZfZIJGStO2INug5Wd_KLDEbNpdZUyfJvvDmYH6t7Ga8nsgTIf2E3Wwv0Imd1j5ACDilO01FUvPqi_vxKcIrstMfBttuxCy9qdJFmVbme5OyMWeVTQNF6ymXvZXnEvkPGcppKWZkApeAAMn8CZ7AFSlQ-ZuQeKP8-3a2DcfbRjEykP5uQl_0T25MDZw0tARfPLWXYIZFaAZ2T4pzaS4aXq-DUOUkScG6jXxibzUQ6CraTg16YoajWRg3ffuPoRZuWjwqCSbAlOqZfsTWe2btsSK-ODZz6geX0zIHwJHwCBfQOeLBZKIqTtIAp1XepaEgd609CpSRVBntgxI8QzdPcKS1DN3u-ahquPepV_4MeR22813bSkG-yFdDjRzRnhFqwTC7CIlaj-D6y46llBWN-HRVw7lKEKIQN3aLo4AGWaTZ2g3AEu64rmeTuRDLbtTV9ManQSGPSQSY-vfEKPSq4ke9tZzTUbqI1ywItv0CEFJcsa1GS9EpxoQsSyEKFzuj6CtQNaWSG2TN9mZqFPerjcUGT8kphLJ5x_U1HCVeTeUVBSAaEqbKitXjdeLwtipJ9qKO4EejK36TzQrQlx1MSlQGx1yVcFkr1EV9k1rgdoRwYd3wBQE61s222bZ-JBO3-hQDfLJ9begl88tkuxwj2POetpAQbzRHpTYhDi1qIBi6n66NtI-BdU-uodcS4fC_FfCb_TEXTscApXimc_W1iFtLnbDnAnsIQER01PieTgdBoR9VUUxoswM-MB3iAps2x7Rasr0ZxD6D9fPacnSnLOZgSlu3_IaKCBHRcdLQBFv7QI1j7k24qCqvYnMTnVKLm2ZgeP066Uo5y5F0RHFYEb2IVoC0Z_b6h9w2vkUrhkBjogL4lwXLSgsdAH4Z8p0Nxr99TWCYi_YjwxKnCoJUJ2ulNB6NcxJJzB_6fwZjAHo3P-X19-blypVzdcNwqNpXOcXvmF_kBAdYGKF7UxQSHbzAKIiMsjSE66kEJcZ0CJMRJyAVhKUV3RACsXg_Z13mkvGAxAQ-0VE5QfFIRTfS9Nn7rlOlaxVvaYTKMrEY1OJl0MG07WVjoj4Pkzt47_FdQiauu1z-AXMFlIbnlX2PJMo3QdDWft2YeZsAqL2Mm2uWOnWhyjroji2QjMlDX8tC6v5znMzeQJewJl42mNQ-JOvtZAdDAV_YY1TfXi_-vGk_T8F5ZhzDrxJKwt5wNXqNJycS4gctuu0N8L69w4MJHn8Nq5JpbMQqYQ97Vq4tvE95SzAvkdHFSbFvfEzSIHo2bDlpTrTGNF9Qv1Bcm4MaRV6QD0zqjvMxn0W8G_qS0QwusKxcl0MvTji6YsfaDha2MT6VUGuvw1IE_dEq6K0SDRVlcyZX9BUcNMs29G; .AspNetCore.CookiesC2=jZM9ABVLnFoCPBiqcP17h0JOR-LkRGDrzqH_xgjpebzShDHVD-LkXUJZlWpAqS1g4T_HTOpAyoU72v6V3jYj6vXvsatQhAJkbns3jRN3wgco4xFgFqbxeU82tEcxYWgSfo7L-UIeYSC9N0BBYwSOElF7ZvUwGJ1A9WgnzMgud856Yb8Y3TE9qmwDtYLbAJns3iXvSmXSUsbiy0Z-3XMtdzRZVa8lVncsc3rqxvuEQ_-KVPhJRiJYeKChofK__6QKF4bzxdE3f6F1P3MQu2LdyVYkWhIfhkrYppxHdbTyNv4ljliRvg2ocU6Mq9sPR9GB7NyGrqDK6yX73eeBgnzAPLEJ9dLjv1-dxEIUqFWvQZZUwMJY3wVf2Zv65q4a_o1UrWhLNosoawUgeBmLalYbYbvYj6Z2ZFoFoERfCpTNGLMJEOsnyT4QRpo1F57YE2yXHu8Jr2cUEy5-DIGQWCRuBYZDWv2b85ZzV0qFAmhZsNI6ltE_UFhXwKpFL-dPSWwvZOCtOeXli2ZSc53tuUbZXB_L89KvaSmHl77aKd8QiwI-JCSrII4Wv6TPIw; auc=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.x3g_IQPAYjpfuX6-M2SLo1p4By03RALJBsEmhbJncpk; aut=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6jv9MQIWFjRiWa-UMcMJ-z4AJhuZszUtDNmrC04ccKE; account=yunfengemail%402925.com; nickname=yunfengemail; uid=ae8d5068-0d04-3a32-9a27-2fff212907b3; ano=undefined; jwt_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.qB1HqxHcK-rdMsU6lKrzfdBtIPBIhZy3XdTq9l9xizk",

        'headers': {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "authorization": None,  # 设置为null
        "cache-control": "no-cache",
        "deviceuid": "5fce6dfe-5898-4ec4-a30e-9eadd7ef754f",
        "pragma": "no-cache",
        "priority": "u=1, i",
        "sec-ch-ua": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin", 
        "x-requested-with": "XMLHttpRequest",
        "Referer": "https://2925.com/",
        "authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IFF19e6N5AR2vQH4tIdIhpcyogS3op3tOR3RDjHncik",

        "cookie": "returnURL=%2Fconnect%2Fauthorize%2Fcallback%3Fclient_id%3DB9257F7F9B1EF15CE%26redirect_uri%3Dhttps%253A%252F%252F2925.com%252Fauth%252Fsignin-oidc%26response_type%3Dcode%2520id_token%26scope%3Dopenid%2520profile%25202980_client%40web%2520offline_access%26response_mode%3Dform_post%26nonce%3D638844675626139171.ZGQyODcwODQtMTA0Ni00ZjNiLWEzNmItZmE0M2RlYmExMjZlZjg1OTllYWUtZjc4Ni00MDIyLTg0NzQtZjE2OGQwYjAxNTgx%26state%3DCfDJ8LvNtK7K79pCgl25c3WvKz1WJaf37cvYaQyI5-Wn2G_x8S4N7Zz8FXZkEuRdi72tsmBVNDLmWuSnlyBAukTlvSFX9tZjpTMFnBS8D30QqCp_i3Wmj9AnY4Q0iZtIL8M-QhuSidDPZoLH_ckqBXtbHSGcs8w_LzovkD7cJwbdsLykg_T7ewFK9FsWYA_ikdjNPAgBE34zOSt58AqzywoslmagWHQzFa1--eijWw7ggGAdLSM7HUUOf0iUJh1TIteR61rKJ6YH6XnCdxb1yrxWYDXNwh91IQEIHp1HsIVRhz3uqIYQB1-xlGdILAHHQEGiue22KDA4fSE3HMCqYJ3s4qJ98AFTwFmwq6KOYWyrJsKodpv6svIFLRhTYHqRHV9K8A%26x-client-SKU%3DID_NETSTANDARD2_0%26x-client-ver%3D5.5.0.0; LoginType=1; .AspNetCore.Cookies=chunks-2; .AspNetCore.CookiesC1=CfDJ8LvNtK7K79pCgl25c3WvKz1s5LkBNfGwtqeiPEP3jcy6VunFvNpEJfBatq3--OSPv1d4CLW385AgbT_tsFY7n9ImNjC6kgYeg5WhGIErthWT_MKLd4pZs2xdcyCyOnVJeZiRQm__ep0kzV7g2THFOUeK0vT-cLiaiN3iStyo16VQKqdre1EfCcWxE9MfgPpSoKg4MYV32TRqCHc-MVcC8zY-QyOawEXe2yHXQhbFVQysGYB7IjjVMlBYUYsmOGZo18QJLykwonC5c4ZcL7oWE-5eZtehTU6hX2Gp-JMtSGo9ugAbmCztWVbqqLYFp6tFoqJNYZyv36C7k_vg3wAcaNsI2r1z4BujOibOG3EoIoQpZFP4YYJK4BT59FLygXBgXGI5aANp_3HmDOpt7yLsqUulscakoGfrgttZ9ys6XAqnBcXJL90dkYvW14zSrwqxmprr9JofPSSrLAHaMn1guuy2GAZSPIVV_7yPCi1gOz1oevmuCSmhL-qKmfgBPqDH8L2SsgzQ_ZuxeBtembkN3qw2Z3_aeIMRwBnL-8SsJIp0sYyxHiPUdKrRPgbwpgcHuyGAGV3LUKfzUq8P2fuXqwq9124IlEYoxKZtDmS0Z7s9rfS_pJGjKTrNKEt8gsKJqMT__xRoyYm77AMUXuXIS7uTYssc2jBg_EVS5xQ8ctPgugxqaDZ51pBPgGdz4S1SvIxeY2sef8FW4QTdzf6iX-qI2z8LLtYmx7zIFurxfKCZ_UMPFST2txsxdphrDoktRxpVRmrfVDA7aJOJXH2zG2-NCDsktcEaEjKHdTf6ap5ykgF2CJhs26Ehnqb73RZ8S1GBlPYOnP9eMnTEEFzlEcIIs7fs6ZN7NZaSCOOORgOJ2nK-hYM49crvmFznxpb7ysu-JylrWWH3eLyVpTuVzUGJdIRtrCJaB08Ld8yE8--C95VL5ZjLuB_xsOIWJHxNo8g09vkBJv1h8yAL3oHyNPT7xbLOPCJJQAQMCcJQAnrSxKB59o5usQiTY7vj88-m1TDN1QQYrL5D4YSghaud5BCXBlmrCGlrYuhgD5-TH0dd5hPCXTN4LJTdee0AuYE8w1cunrO8Et-W7s6g3F7hzF83zb_Zpvh2gjVjPB2Hnlm4WHfxaHpU7_ZVZLK2kHFu78bUZqSOvrL_c5OxtfeP1AjwH2H8YXKfXdMziLQ30A_fkUtpPMUc2lPd2GlcRsS0IKW8QvBwK5EIg42JUc00PaolOjm5nraaQSE6lJ_gEDrfFJf_5GoKZBAawQkhO2jllLQrn6pv55qbfBjBDN-_JF63jBMHqGwXmTKapTmNKNJpgt0qLplN0RFew-zTIfEEhpH3kcWhrA2bEFY0dGgILtw-v5LEj398ZA3gVVgX7h7zVmGwU93Vaxs4Ij32hbenCFfx-TWihbp2ZcSTkCZ5XPR5nvDQhbI_k-L5swAnx8J0b9UoyatunoL9uoGoiEB--RaM8hy2pUlxyUh5D_au7TP6hBRS-8t0sB0fs7kGDZyiCQfMJzZk_E5j4HPpMGg7WOHN6zmL_00UbuQVTd8WBUafhGE09ZkCH705JdMDxRJvJaa_TX4svBX5A0ajMezonUGSLH2Fw0u6HpsmfvLY14Zn42dYhHpG2dyYkOBDI3b_lVikfn3i1yM58NiyTfW9UvOMPp47wywFBiEOkBjbvVxSlQOkA6wuZpQdq074i8zzhv3h7bfrq0XDakGmSXdwWE_H5mQsMFQtvSr3qT74d7Vd_xsywVBsr0LWfS1IAAms9s4u64gylwG-GYPiyjFG7ngRYRbDyKgg6UgQwkeeNCKVBIzeTbr7GpHqkghCF00CnYTGxTMiqiuwK3CdyOl84h2SxSj2eWm_vNvNCbp_ty_EFyybUSTkkUoAMdBsgOQZV5WNKUS_SpdnrTHtlhUylYeGQP1a2bzIFd3cGlfz0arMZdx5K9JxGPCNuXczLxDJ0AHV8t8mvtU1LI1ot40PymJgStNAuzIyzqTVjnqdsSYRXkWulcvkP4TOnOjgLd2QeJedC__80W9efZNbfqpIlX8bNty4YeV07H-GVoCFoDe1bf8poZ9092T5Q6VyKQSGBXWM7q6zZTbbs9zwAZAQZLnGVlWLnQGB5MZOj4bO5wYtY0qWuy4wTMRPqWr58BmEZty6d-H3pPR2ZDhhZo0EAgAorQPLXmHrgxVnpmuy9ChhSFBdYsHBDI2R1PhTMUIw9dbXGGJhzioxC_ULSyAZ1NubUINzdBHcbicG0T5VmxaYqoeY3IVV4R1dLqzLV9_-1QExJaYJwPP_OgBdj6GR2uMzIi5m15Do-nM1E5f6NWEnM1zzXNL74EIUJiV7yaHelnZfElTtHH6x7OanxadO04q5jHZxNvDRBta5gdHfboln6qC2QpU_E5cUcIfIeX9n4a0p3rwr6caUs4klfbMsXFkmTS1NhxuMN5sH0Y6ck9POnRN8_u7KLF2Dd3INcZCngyJMTBO6s6Lmq9aE04S5JHOkZnFdwJ-ikx5h5stYLAPLRXMwoYBUElQLK2U060YKcsOy3w9KuTdi_PPI7mQZu4P11ka621JW_d2qA3EHTzjNIGSFmqyFUwfneg1JJQjDIHeLLOLYnxOCT1xDeZdsHBXvqkcyI-tIVm2PkQE48M7reJJi6OwP9h1dBol8vvwI4MgbUlBF6Q56Xwbv7A8pFvrMSeu6ha9m6ZZZwuEhZeMiH_NC_Qf6G5Jm64WtjKPILGB61T5mbal7wJUxEPaxPSpQanOIt4ZjCtcjNgRZ2JAJi8WgbnJkyZECsOcJmNapTBnbUUS3E25-tgMIatHmHfjQLlZ694FhFTEkzrW4oVaa1wVTUnoUAG7tS3LfZB0bvI21F9TcMB5g-UFbw7xmt8z39J9WmcThleZJ69Szx7QN8BKwKfVPBORAiSksX-N51hQrSqchMA2rJShip7odcyRAuL624eC_iBMtsjHT0euYijmWfETQZ0PHUN4I7dr1lK6Bz1mMDy_vtZCka4Ry70Plj25Z1Wf6z_uQWkRtpZRoGWN7jO1gP2BSherDgeeGVgD7Oum0uBjZRIshcNA0no4Bu7Ll0Vyh8utahGZT4mk1eZfbldZcpYUEU8s1Nn5Ei33YRCFNG7_IZSJ_z1sKV75GDPKfz5fXMBkGFHwAHnZzs7vwJvdszCAQGsX20kGNhYwzK9lLz8ePUxgVqWwgRqZmcRwDI4tN0jelx914MeGE73tJNyiF18DZZ0uaso-WvdY3fygJ7XaD6DD1DMHlkzceBj4F6ZW9QZh8WmYUybEYAX3rE29LURuUS5mkG-I8Uaas526k298T0dRBFtycUhuy1MMjp-tIbs8M3ST4PDRMHdfERp2zJxYFuz6Ci-m_u7tYrF945pAj5ybbEeeE8ZZM2rOJBDwYzViNTPnjxFVemdxZ_u9TRv5Gs8qEFNgtsDR9aW1mqw4miyHrP4eY780eUTt67oMvH443Jy5SiMN2LpUTdjzOxoBXxU_xHV8ijgG29R5gAHLGsFLQbkJhSVy4fE4QX1p0UIiwBoUM0CPgp7r-obfQObtzgow-X_Or-GSIUCAcVMUfVXChvg-ejnLiZ7uKNVYcsdUqcCIn1iLPS2bZibWkY5VKnsJ9AVNE7Tr9VFhjibiy0ggxFGa-nCCbMy4fzc4UwGx52Ggmjtip6CPiuTxm3tYN5OSRSAZdMWCQekW_A9dtXtIwln9qBoRqH98bTF0kRiobbCur-Oovyw86NIbmYnOk8vXC-VgszKLP5Be_4TGIJAVV1PJN4m-PwDxW7iK3GPBKOwdYCPgXzDYGQQX-1AzEy0uvuea8--JZUtX7E9wRNPbp1nRDAHSwDPj7O1Rmajwv0esZ_-zRF7-fVVAb6LZbNb7rsIbihC1uUlSf0UVlY33zFgqWkgupGYxdnwWwIB4QtWGsp6dDoWChDBEEzXJBnmw0hfPIlSIm6SBciHcah0Y1Xlc4X9Yf_sLYfL5J3DzQxisPlTLOFcvEq; .AspNetCore.CookiesC2=DiCxRlk1QZ7x9tYOZV1eCEhVietShO_Qimb_syskmWEckskQLkGoadbqiAZ6SW9iNYfWmxLVUzEMoImFsbdtophlHOmDgag5PFZ6Kya5xgLLwlrWuApHiivs44S_UzjMo4KPW8IpMGA039cY8r8ANIebKwSN8vlWCAFjiYW2ahsTpMQQJGtyiXfg1aqCQR0dqXVY_zH-c_yAX4wHHa16y_oKcJv9bRgifLGXpIhWc9euGxPEVpJl2UIuZ0XiQ6WXgoqhHisPpUBvjhMQWWnpJOzU3qe2ogin0G25jeAdX4vTE6HK65K71k7wTcRqeAByiN8W7ITwW2A-wqXroTe-AT7wo6ZAjGoQO5Wp6piMleeJOJMgXI_YaPoz4z7LWYZoMjULYymN8e4vle2yJ9Iq0XFMx-oebPz2mUAReOmBV0sMrV9c6gIq868396fncYCZhKeObV49B71P2IAAUJscL6ubCuMZSk2-VV0l8UYo_w68Kt2ZXRn3oOw_6WXoSampsRRr7cma3-hdzEReTZqMYuM54DIijG0nqtYKZY2KqwCqkwp2NmtHw4s4Tw; auc=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XH8_Lp2iTfONXGZA-tp65hAhKc7ak0df1HW37HqJpX0; aut=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.m2_7sK9EWOZ5Vx6y4wL5_05XV3CUEwM7se_LjHQdi5c; account=yunfengemail%402925.com; nickname=yunfengemail; uid=ae8d5068-0d04-3a32-9a27-2fff212907b3; ano=undefined; jwt_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IFF19e6N5AR2vQH4tIdIhpcyogS3op3tOR3RDjHncik",

        "Referrer-Policy": "strict-origin-when-cross-origin"
    }
        }
        
        self.email_routes = {
            # 163邮箱路由配置

            'my-moon.fun': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                        
            'my-moon.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                        
            'my-sun.fun': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                        
            'my-sun.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                        
            'txmeitu.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                        
            'txmeitu.icu': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                        
            'jdmt.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                        
            'jdmt.xyz': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                        
            'jdali.icu': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                    
            'jdali.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                    
            'meetcur.icu': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                    
            'meetcur.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                    
            'cursoremail.xyz': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                    
            'cursoremail.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                    
            'curmail.icu': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'curmail.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'jdali.icu': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'y1234.shop': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'y1234.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            't1234.shop': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            't1234.fun': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'e1234.shop': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'e1234.fun': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'q123456.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'q123456.shop': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'q12345.fun': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'q12345.shop': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'q1234.shop': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'q1234.fun': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            '00001234.fun': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            '00001234.shop': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            '0001234.shop': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            '0001234.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            '001234.shop': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            '001234.fun': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            '01234.shop': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            '01234.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'mmm9966.icu': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'mmm9966.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'www9966.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'www9966.icu': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'eeexx99.icu': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
                                                
            'eeexx99.online': {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            },
            
            # 2925邮箱路由配置 - 每个前缀有独立配置
            '2925.com': {
                'type': '2925',
                'prefixes': {
                    'mailtoson': {
                        'api_type': 'mailtoson_2925',
                        'description': 'mailtoson前缀的2925邮箱',
                        'cookie': 'returnURL=%2Fconnect%2Fauthorize%2Fcallback%3Fclient_id%3DB9257F7F9B1EF15CE%26redirect_uri%3Dhttps%253A%252F%252F2925.com%252Fauth%252Fsignin-oidc%26response_type%3Dcode%2520id_token%26scope%3Dopenid%2520profile%25202980_client%40web%2520offline_access%26response_mode%3Dform_post%26nonce%3D638835173961494183.ZTE1MDJjMTEtYTY2NC00NmY0LWFiZGItZjQyOTcxMjQyOTNhMTVlNWY2NjYtNzA4ZC00MDMxLTgxZWItNGJkNDIxODljZDQ0%26state%3DCfDJ8LvNtK7K79pCgl25c3WvKz0N10RLXviZGkOd_wqYDixyuduOmHncH4pK_BA-bPm5n6DLYtEukbHLpajBvFlOChkmTBuZxG40uJ0HDJNXh_OVjUq6y_Ta2VxrtSCJClU_NkbUkUFHMGRywHTMF6pRJswvudIsWYJPDc6uuL3VA5SuUKd4hWFUYSQ7eAW76-Xm0ebUYIOixE3lvqYg55p8juROYOfAyP-LLcpvuB6yEFpP_3cNP4tclaFjMdvppuDab7XKCYCsl_0xaKFYTbZim7-na6JMl9tAAgkbzkvPfd9ljpo1iym5hZI1r4tcsvTs-kCEiitWd6LI6_cw_AQVp1lVBpSR8eMhvyl3MzhFUAOHjZ7KHxgDP6UniHfi4beTWQ%26x-client-SKU%3DID_NETSTANDARD2_0%26x-client-ver%3D5.5.0.0; LoginType=1; .AspNetCore.Cookies=chunks-2; .AspNetCore.CookiesC1=CfDJ8LvNtK7K79pCgl25c3WvKz2pamLs7qweHNU3JmZswawgUFTHVwSW2hmZAcp2AIWGfWTAj1BhiqZSEFAr0AyGUOFG-fTQ8AhDu8L4ZV183RPJdHS_WT6wYhzJW8Gxe8TbkMmQy7vtCjKYbErK796v-tYUoIqUsslnYgV5j6ohPRuEHbaL-m_ct24LZSZnpOv05m09HkGtyxDxYW-oTyA57-NW2paJLx_sHvojNbTp10tjZ191v0HGM995YRhMzPztKG5oWOQ6KcFt8iz-E8qxTyt73wHrs54yoTVT-6ETleUERwqXtVWyu5FiILjqs1TeZJuLxGQbzQKYP5A9z8wrhdGh98FqYptzJm8fwJaIqhoQd1nJWxt8doejqO9W4c79NG52fRDkvPqOfUXsmUS_Vx1HOJfpO13AGgSmeBx12SVAW3MEllx8MiHGR-P0FG-iTITV2JGJBktrlWk5wEgO3KNA3ykahuMOY7TmCjN9g-DtX_NiLu4lNX3hG1ld0__MLB4lZVaIlX7Adbinu9qWr9X20x_ji9fZtaA8Z5kk368gxBlLP-YOYS6D7SOE4yO0INV61tFdqtrXnksz4XX4v7DLvTeQjVPZCKcqLP7uQ5D9RQCNFuAa9cy-R4hLd6a_-1aRc56rz2IJ22JkfOvWaM4YafHrLVUDZd_uJ9ullX3sTzxw7ufLd4zVTJ8OPoR0o6unFmOHknvH7U7neLIsZ6osndOsGrxp43HgH7Iiapm5OYFfq5yFpPKwc891XvkeaJ1bogqbQ23d4jkRW2xNAGcId7Wp3rWjJ2W5DjZBwIQhbRXlrKZOjdkHrhKrwGCzP8aEO0lnYYIx5rPLS84i0fJMZ40uQl1ff7i7rxZ4vTRcMK7NSoDHTaALWeDqb3_-qgCNXnCx05fo1I1smUtuBgyLNsAXAm9MP8eO85o4RDKZcjezQVpYyvE1BnRBF6Zx1l-4LRroNZr4DZtMY2NRZt0OJVDKvhGhAS89zuCwBgc3qOuZmewe86DzR4NQv4lrNeTwy1hYqkq-ewUozLCNIPk4GmZAG6YOAGZiRWV3BQlDgAiY0ZBSa9domxn-Gs9w9SZjKCsXCS9ZTvgGr_-aB45G4XXSQgpQKuYlA7eIchNoBPs6hpmmYo3JezO2QHC7hijB5CAFYSrLoFjZXONOMWtQ5CFIo5Ix8F12D29UA5cZR4dFfonMEHDC7cZANTEfuctUTrc9asmHC6vrRYznrssNWfU09V5h0vGQ28gjokzuk1zhAfhQJNe2YE_JRu6U1xFZLSufzVOwFW222uTlk0yAOGhHnmE6tKCO4UTOxF9zSaTXdpWEgtT7YHKxWFOJdWCRPg9H6pJDvd6K2LUZQ-BHYjH0NT3ldbmsEqnx0WuVgxh6aHH6V7KvQ-StT_DjpXWZD2Vz9AE8kibw_6zldM1rtF0tXCOMdQIj4qluuMMl5R9FGF8Oq3s4LTWooTLnMx4OxHXYq7Dr0IHg3gxRj8ghLGejrHEFkrh8bwA4BBMXJ818XbcwLxa9yYHiRZ6Xr8QK_09FEEIbuaOGDX2jIbAAXSPJCBSyN7ILvzHvOHam0Tmq7rJUDkvetrWOBpUWmi1D2AZYDnWLU6KqyHuPmWQm1cxMxwWxVTB0ASQLgvNIHtl3BnAhhNRmp9_k5Rn4ZqYbAeoFAvVFnt-Lj5s3LhHezAaQhpxlzTmBXYOZEF6ctIyZ4ODjR2jBCzgO9z9r-R7XTLceKD0d-ocrGNKyD0-2_VSypB5-1G5v5gc7tv_e-d0d-R7b-c0HLmz5qe-huiwklO4XJZjVESkd4ALyhDk6RtZzEBvQa9MEq25Ql5JZO2ZeJOkZVEYpetUaeZgIWqV8jdXGF62D-r4JpNEjd460hEebsKao8mdifPl0r_MPG94_4w3bmapbpZbcc6hN5ThON49sIXquIUze3FcVTJ3WD_y9yCmJdaPUBqrOh6HPbVq7EavX0SUiROf9uTaIUbmb31FTsN74W4GxdFU-L633qnCL8NSKsmLofahxmgqWIHJ6EQ8eMHxyAskkKnIucCx82eAQKSWoTxYLWVy2zKdMsHHTzq4BbUifozn0j4Qz1NJB1Gu71MvMdyoeXJnCveWmTopSjsIV_7mHxPjMlvChxP2l7EUvC8pr46vKdWiqpK4r1J7iUFPlIqVHO77a3IVPaldwzH46eNJWl5TdVoI8maHi0nEKzVWvaFw5wKWQ-sDKlRrDxPRl3GpjLxJ4upN5A-RV2PXCbWdXbzfVAfacfxHvZMMfTbeBFB1aMOKCgyyI9-nNzD8yIlHUdoP_O9dUUGX29lNZ-ePzMDzIImRs_RnU7eiOxVqDi4vlzEknt0A2s1O9DSt2wNIZ8lDGDHthFPt4VlBNZ3Y6FVRvZBU-9TJd7cHZYY5fFUbtlU6ioZ5Hz3kusAhCbGEFtzehfHdxXXBi4EPbzptZmy_HCNvZtoTwJMyVYmioUOeJp4lRQ52ozDqTpOs50Gomd9T9YbVW5Vjueef7WUnuM5Zkx0mpez62Rm911JiFG2r9Sg3n5Zz52E3aYVE9Xk5XbmDwa2RtqYXpPwAvpZQVaz4cpdpwEh2uIpbgbI0yA5s5H7I7R2haLtsz1OH7-oKkMF-p1szIExfRukBKNt-Vztp_FKLy9qgp1ZW3rbCC6hRHzKBCoONsx7fZQMRV0aGDrwy-H0et91M6biHSSgt8Vdz4XOCon_VdF69j2PFN4swCfgcP6it6t6IFn_8eFX3mK2Htfrpy7LZbdTtfAyYzui-EIwESh9TDB1crcTmRTaowADZ4yJKcCBmxlVgSIjKxqvp1dgNegzUWOc76ZJDF4_XKHzjyoQAsAhi4GkbIFsF_A4sTTy713ArLBbrJo5maDvm7bZwA15QNWfWkzVGyY3WzBZu9OT6m_YVwkU5wmmWd_Tx5QbQgNiEQ48fwyxH7kZzjMgmm7ESZAUz96CZcquVQ_p3iPxyHvT-poHkIjC2SzQtJLTsN7siWk_ijcyeuI36MyfmLKzO-JUUJVto3_lwZNc4j0Vhk4wrIB7JwYYwvmPE2wjAfaSdhrdDr8Y6JaEFo0itMh5Fa3v9r6C2Yt3FCbl34X0cLFTY7GY_XLjDnnroWmV43uUNvdyKxD808CPVaI1y3ZFkqcYG-nXNZJriujgoa1GmwXsMAv2Sldd8jXTEx6PTDPwkREyvbtDpL829XGcwd8w-Dlo9ei4JQ3giycAjLYrsJgvPFRApqU7-LUjakxYKieNKmIYix2Fx9us-8Dta8Qy9UxwSZWVg-yPppzz1jsH8WOSQSmRhOY_A1U_xkSwdvnbU_6iZu0wKqSQ25IjHmY1v5wT0E3Ry3S0p61Fea1qqqr0ZkEW6-chZnemfAAWHAODBYNOq3cW42Yeb5lHgM8riFlcIZcOkCoU8NQS_3u3ed3j3SeU0MUa6-HcluJtrSRy_CGhU_Pej0fGSaPT0YZh9vzIrs8hPtMB7IAzk5F2Mnzyzm0MeM1kQVpSj79H8l5HITq-tfByeNJdCPa9GJDDPrzQxJWyQf0T22xVc8Wl3JHts-Tnu4fRYx4SXmMWLnnTXXKm7tfPRE-u8ryV3UbiuRm8vfgy1f_O-QLnfGajo0POo1yrkf7VkOJZd8yKuQK6y_Rb-8ibX4KbMc3EWscKpZezwDSfPyOi4bAbkJstsWtEYNhlBlEILt2ju3qXO4uxjv9majPp3sv8_l5P42hLRD_yC_DYskVch6iyo40tZat8rEeSGACYoIOpUH2xVopdZB68bo2vIgjkHtkTesiIBny-JuTSxJR-Af6LJj1gT7TZL9dz6DbMLTBdPQdcsBDgNfeBSSJq-icx8eAd7AY2Pf9H7XHcdVcaQ3QYJpsqu8rCsbEp1Mm21qB68I2kBDRNq0CyXN_4YtIEV2KzPD-X_80wnrfhkmBVBE7FJmkCiY4bp2wHHPaCvIstrI_mGLIlHtHxRBsVCFANUIEVD4xpKpOiqIa9ejGvMbb6jKMu1NM; .AspNetCore.CookiesC2=o-r_fLlssiQpJ1yy7e8kps_k2NS4JiZRMreL7a0cXYM7GZxD7O3_T6HTvgb8CXunDqepP3on-5u69RuIgelLMRptZXwT_A2yZeeuyka_6lr_j2Q0x9jBQxy3cqqJHhCUt0qS_Nd5rm3sNU0AjIVSdLOJcQNhCs3YKlw-QIj21zUjtjdA4ZVn-zvlxG78pVqvN_sAjOK0EmhOU8aU_CqQjunL7JKkiodVq8O89RsurhbIuJdU471_MYoN7Z9wQcRKA9mLwj_OJwUKT4crzD_x42RBB7J72ux7dx_InJtgHiAUF6xabI1h011XF_iXpiD7T_BpsjjMBt71U10uMuOOwTjqioBLo0Pfv55ytv5Z5Jv8CsPAmdhff4B7rZ0LxQEhgr-3PR7ZBjJB0BwAI3BfCm9dz8SjOqsCjVr2mh_RoPcdbTFfMfFf7fjiPd_Th0CGqsFyBmKh8IWMA12RjP1qwyT_nHwy07XtHRsHBXdinHjqew7NnOCy0cvq_1JXa1yLL3nG5O9k8Cf9Bhn_WU-W3FFnD5YRzjfqgJdkJU7a_KDiI-7pm9iQ-WeGkA; account=mailtoson%402925.com; nickname=mailtoson; uid=5e32c9af-454c-305d-a75d-f59f4d9e8493; ano=0; jwt_token=eyJhbGciOiJSUzI1NiIsImtpZCI6IkFEMUZFNTBGNTM0MUFCMzkzRkM4RUQ3QUZBODc4ODIxNkY3MzA5MTQiLCJ0eXAiOiJhdCtqd3QiLCJ4NXQiOiJyUl9sRDFOQnF6a195TzE2LW9lSUlXOXpDUlEifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.nWHn95-Z6AMLxzVl2999nhnJUmywrl1BMtoRZiHIT-eIs1QqxS3NttIXKvP0-MfsWG5xT04Tg_JDP5g84iOJ08fhcGttSegMWgPI6EI1Spcfi7RRdv4-SH3_7nuxz4I_D99ZtR3TRBWj5OskERFdXINlQiVHk7c7jBy5Qs24BSxRY5s7A5Kv9R-yrzRy_hqbruP_cQjbPGT2MwMWZCuXRY1IIXIUMRp93kRysOnorzZZGywaFl641RznKHGbikmEZt8E6ZEvPiUIjfchDA1Ph-TnoNg1YjJc0YCsfBcFQF-9nKxWTOeu1fWXtN9eaoX7W41zJVWpCWh6AnBwAjbZyw',  # 需要单独配置
                        'token_url': 'https://2925.com/api/auth/token',  # 可选，为空时使用基础配置
                        'device_uid': '',  # 可选，为空时使用基础配置
                        'jwt_token': ''
                    },
                         'mailtosun': {
                        'api_type': 'mailtosun_2925',
                        'description': 'mailtosun前缀的2925邮箱',
                        'cookie': 'returnURL=%2Fconnect%2Fauthorize%2Fcallback%3Fclient_id%3DB9257F7F9B1EF15CE%26redirect_uri%3Dhttps%253A%252F%252F2925.com%252Fauth%252Fsignin-oidc%26response_type%3Dcode%2520id_token%26scope%3Dopenid%2520profile%25202980_client%40web%2520offline_access%26response_mode%3Dform_post%26nonce%3D638844675626139171.ZGQyODcwODQtMTA0Ni00ZjNiLWEzNmItZmE0M2RlYmExMjZlZjg1OTllYWUtZjc4Ni00MDIyLTg0NzQtZjE2OGQwYjAxNTgx%26state%3DCfDJ8LvNtK7K79pCgl25c3WvKz1WJaf37cvYaQyI5-Wn2G_x8S4N7Zz8FXZkEuRdi72tsmBVNDLmWuSnlyBAukTlvSFX9tZjpTMFnBS8D30QqCp_i3Wmj9AnY4Q0iZtIL8M-QhuSidDPZoLH_ckqBXtbHSGcs8w_LzovkD7cJwbdsLykg_T7ewFK9FsWYA_ikdjNPAgBE34zOSt58AqzywoslmagWHQzFa1--eijWw7ggGAdLSM7HUUOf0iUJh1TIteR61rKJ6YH6XnCdxb1yrxWYDXNwh91IQEIHp1HsIVRhz3uqIYQB1-xlGdILAHHQEGiue22KDA4fSE3HMCqYJ3s4qJ98AFTwFmwq6KOYWyrJsKodpv6svIFLRhTYHqRHV9K8A%26x-client-SKU%3DID_NETSTANDARD2_0%26x-client-ver%3D5.5.0.0; LoginType=1; .AspNetCore.Cookies=chunks-2; .AspNetCore.CookiesC1=CfDJ8LvNtK7K79pCgl25c3WvKz1s5LkBNfGwtqeiPEP3jcy6VunFvNpEJfBatq3--OSPv1d4CLW385AgbT_tsFY7n9ImNjC6kgYeg5WhGIErthWT_MKLd4pZs2xdcyCyOnVJeZiRQm__ep0kzV7g2THFOUeK0vT-cLiaiN3iStyo16VQKqdre1EfCcWxE9MfgPpSoKg4MYV32TRqCHc-MVcC8zY-QyOawEXe2yHXQhbFVQysGYB7IjjVMlBYUYsmOGZo18QJLykwonC5c4ZcL7oWE-5eZtehTU6hX2Gp-JMtSGo9ugAbmCztWVbqqLYFp6tFoqJNYZyv36C7k_vg3wAcaNsI2r1z4BujOibOG3EoIoQpZFP4YYJK4BT59FLygXBgXGI5aANp_3HmDOpt7yLsqUulscakoGfrgttZ9ys6XAqnBcXJL90dkYvW14zSrwqxmprr9JofPSSrLAHaMn1guuy2GAZSPIVV_7yPCi1gOz1oevmuCSmhL-qKmfgBPqDH8L2SsgzQ_ZuxeBtembkN3qw2Z3_aeIMRwBnL-8SsJIp0sYyxHiPUdKrRPgbwpgcHuyGAGV3LUKfzUq8P2fuXqwq9124IlEYoxKZtDmS0Z7s9rfS_pJGjKTrNKEt8gsKJqMT__xRoyYm77AMUXuXIS7uTYssc2jBg_EVS5xQ8ctPgugxqaDZ51pBPgGdz4S1SvIxeY2sef8FW4QTdzf6iX-qI2z8LLtYmx7zIFurxfKCZ_UMPFST2txsxdphrDoktRxpVRmrfVDA7aJOJXH2zG2-NCDsktcEaEjKHdTf6ap5ykgF2CJhs26Ehnqb73RZ8S1GBlPYOnP9eMnTEEFzlEcIIs7fs6ZN7NZaSCOOORgOJ2nK-hYM49crvmFznxpb7ysu-JylrWWH3eLyVpTuVzUGJdIRtrCJaB08Ld8yE8--C95VL5ZjLuB_xsOIWJHxNo8g09vkBJv1h8yAL3oHyNPT7xbLOPCJJQAQMCcJQAnrSxKB59o5usQiTY7vj88-m1TDN1QQYrL5D4YSghaud5BCXBlmrCGlrYuhgD5-TH0dd5hPCXTN4LJTdee0AuYE8w1cunrO8Et-W7s6g3F7hzF83zb_Zpvh2gjVjPB2Hnlm4WHfxaHpU7_ZVZLK2kHFu78bUZqSOvrL_c5OxtfeP1AjwH2H8YXKfXdMziLQ30A_fkUtpPMUc2lPd2GlcRsS0IKW8QvBwK5EIg42JUc00PaolOjm5nraaQSE6lJ_gEDrfFJf_5GoKZBAawQkhO2jllLQrn6pv55qbfBjBDN-_JF63jBMHqGwXmTKapTmNKNJpgt0qLplN0RFew-zTIfEEhpH3kcWhrA2bEFY0dGgILtw-v5LEj398ZA3gVVgX7h7zVmGwU93Vaxs4Ij32hbenCFfx-TWihbp2ZcSTkCZ5XPR5nvDQhbI_k-L5swAnx8J0b9UoyatunoL9uoGoiEB--RaM8hy2pUlxyUh5D_au7TP6hBRS-8t0sB0fs7kGDZyiCQfMJzZk_E5j4HPpMGg7WOHN6zmL_00UbuQVTd8WBUafhGE09ZkCH705JdMDxRJvJaa_TX4svBX5A0ajMezonUGSLH2Fw0u6HpsmfvLY14Zn42dYhHpG2dyYkOBDI3b_lVikfn3i1yM58NiyTfW9UvOMPp47wywFBiEOkBjbvVxSlQOkA6wuZpQdq074i8zzhv3h7bfrq0XDakGmSXdwWE_H5mQsMFQtvSr3qT74d7Vd_xsywVBsr0LWfS1IAAms9s4u64gylwG-GYPiyjFG7ngRYRbDyKgg6UgQwkeeNCKVBIzeTbr7GpHqkghCF00CnYTGxTMiqiuwK3CdyOl84h2SxSj2eWm_vNvNCbp_ty_EFyybUSTkkUoAMdBsgOQZV5WNKUS_SpdnrTHtlhUylYeGQP1a2bzIFd3cGlfz0arMZdx5K9JxGPCNuXczLxDJ0AHV8t8mvtU1LI1ot40PymJgStNAuzIyzqTVjnqdsSYRXkWulcvkP4TOnOjgLd2QeJedC__80W9efZNbfqpIlX8bNty4YeV07H-GVoCFoDe1bf8poZ9092T5Q6VyKQSGBXWM7q6zZTbbs9zwAZAQZLnGVlWLnQGB5MZOj4bO5wYtY0qWuy4wTMRPqWr58BmEZty6d-H3pPR2ZDhhZo0EAgAorQPLXmHrgxVnpmuy9ChhSFBdYsHBDI2R1PhTMUIw9dbXGGJhzioxC_ULSyAZ1NubUINzdBHcbicG0T5VmxaYqoeY3IVV4R1dLqzLV9_-1QExJaYJwPP_OgBdj6GR2uMzIi5m15Do-nM1E5f6NWEnM1zzXNL74EIUJiV7yaHelnZfElTtHH6x7OanxadO04q5jHZxNvDRBta5gdHfboln6qC2QpU_E5cUcIfIeX9n4a0p3rwr6caUs4klfbMsXFkmTS1NhxuMN5sH0Y6ck9POnRN8_u7KLF2Dd3INcZCngyJMTBO6s6Lmq9aE04S5JHOkZnFdwJ-ikx5h5stYLAPLRXMwoYBUElQLK2U060YKcsOy3w9KuTdi_PPI7mQZu4P11ka621JW_d2qA3EHTzjNIGSFmqyFUwfneg1JJQjDIHeLLOLYnxOCT1xDeZdsHBXvqkcyI-tIVm2PkQE48M7reJJi6OwP9h1dBol8vvwI4MgbUlBF6Q56Xwbv7A8pFvrMSeu6ha9m6ZZZwuEhZeMiH_NC_Qf6G5Jm64WtjKPILGB61T5mbal7wJUxEPaxPSpQanOIt4ZjCtcjNgRZ2JAJi8WgbnJkyZECsOcJmNapTBnbUUS3E25-tgMIatHmHfjQLlZ694FhFTEkzrW4oVaa1wVTUnoUAG7tS3LfZB0bvI21F9TcMB5g-UFbw7xmt8z39J9WmcThleZJ69Szx7QN8BKwKfVPBORAiSksX-N51hQrSqchMA2rJShip7odcyRAuL624eC_iBMtsjHT0euYijmWfETQZ0PHUN4I7dr1lK6Bz1mMDy_vtZCka4Ry70Plj25Z1Wf6z_uQWkRtpZRoGWN7jO1gP2BSherDgeeGVgD7Oum0uBjZRIshcNA0no4Bu7Ll0Vyh8utahGZT4mk1eZfbldZcpYUEU8s1Nn5Ei33YRCFNG7_IZSJ_z1sKV75GDPKfz5fXMBkGFHwAHnZzs7vwJvdszCAQGsX20kGNhYwzK9lLz8ePUxgVqWwgRqZmcRwDI4tN0jelx914MeGE73tJNyiF18DZZ0uaso-WvdY3fygJ7XaD6DD1DMHlkzceBj4F6ZW9QZh8WmYUybEYAX3rE29LURuUS5mkG-I8Uaas526k298T0dRBFtycUhuy1MMjp-tIbs8M3ST4PDRMHdfERp2zJxYFuz6Ci-m_u7tYrF945pAj5ybbEeeE8ZZM2rOJBDwYzViNTPnjxFVemdxZ_u9TRv5Gs8qEFNgtsDR9aW1mqw4miyHrP4eY780eUTt67oMvH443Jy5SiMN2LpUTdjzOxoBXxU_xHV8ijgG29R5gAHLGsFLQbkJhSVy4fE4QX1p0UIiwBoUM0CPgp7r-obfQObtzgow-X_Or-GSIUCAcVMUfVXChvg-ejnLiZ7uKNVYcsdUqcCIn1iLPS2bZibWkY5VKnsJ9AVNE7Tr9VFhjibiy0ggxFGa-nCCbMy4fzc4UwGx52Ggmjtip6CPiuTxm3tYN5OSRSAZdMWCQekW_A9dtXtIwln9qBoRqH98bTF0kRiobbCur-Oovyw86NIbmYnOk8vXC-VgszKLP5Be_4TGIJAVV1PJN4m-PwDxW7iK3GPBKOwdYCPgXzDYGQQX-1AzEy0uvuea8--JZUtX7E9wRNPbp1nRDAHSwDPj7O1Rmajwv0esZ_-zRF7-fVVAb6LZbNb7rsIbihC1uUlSf0UVlY33zFgqWkgupGYxdnwWwIB4QtWGsp6dDoWChDBEEzXJBnmw0hfPIlSIm6SBciHcah0Y1Xlc4X9Yf_sLYfL5J3DzQxisPlTLOFcvEq; .AspNetCore.CookiesC2=DiCxRlk1QZ7x9tYOZV1eCEhVietShO_Qimb_syskmWEckskQLkGoadbqiAZ6SW9iNYfWmxLVUzEMoImFsbdtophlHOmDgag5PFZ6Kya5xgLLwlrWuApHiivs44S_UzjMo4KPW8IpMGA039cY8r8ANIebKwSN8vlWCAFjiYW2ahsTpMQQJGtyiXfg1aqCQR0dqXVY_zH-c_yAX4wHHa16y_oKcJv9bRgifLGXpIhWc9euGxPEVpJl2UIuZ0XiQ6WXgoqhHisPpUBvjhMQWWnpJOzU3qe2ogin0G25jeAdX4vTE6HK65K71k7wTcRqeAByiN8W7ITwW2A-wqXroTe-AT7wo6ZAjGoQO5Wp6piMleeJOJMgXI_YaPoz4z7LWYZoMjULYymN8e4vle2yJ9Iq0XFMx-oebPz2mUAReOmBV0sMrV9c6gIq868396fncYCZhKeObV49B71P2IAAUJscL6ubCuMZSk2-VV0l8UYo_w68Kt2ZXRn3oOw_6WXoSampsRRr7cma3-hdzEReTZqMYuM54DIijG0nqtYKZY2KqwCqkwp2NmtHw4s4Tw; account=mailtosun%402925.com; nickname=mailtosun; uid=7e5a1391-69d2-3173-8326-21209e71797b; ano=0; jwt_token=eyJhbGciOiJSUzI1NiIsImtpZCI6IkFEMUZFNTBGNTM0MUFCMzkzRkM4RUQ3QUZBODc4ODIxNkY3MzA5MTQiLCJ0eXAiOiJhdCtqd3QiLCJ4NXQiOiJyUl9sRDFOQnF6a195TzE2LW9lSUlXOXpDUlEifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.T6nTkQ5lJjl1Fp0YgL3HH69QQye6DusIoI_QMFSiGUnX6GvPFgok8jkHOuEwou218KfO-exU3r2k9_dAADMGNotzTlB_FGLY_VOH0g1pXTh5SSqlJWgNH_REd9KNF0tA9ksS4AVF7uPj6bUCw0uQUckhx0QhQB8FNHpdhaJJY0CEuNRZDiljCtSX7Tcgib9TzMxsJPEKhx-pu-2EulcUeIayYyItzgszerb-fv5Ah1aywMbgWHzRcEOC6C07oq9nFLzEI8vDG9uKkjrASf9GB2PpmqEyB_pcLSgpTtG_hHfRmrAEG2LErFpidW15Fmpwadn9fnr1wWEXQpX1kN4ZNg',  # 需要单独配置
                        'token_url': 'https://2925.com/api/auth/token',  # 可选，为空时使用基础配置
                        'device_uid': '',  # 可选，为空时使用基础配置
                        'jwt_token': ''
                    },
                    'yunfengemail': {
                        'api_type': 'yunfengemail_2925', 
                        'description': 'yunfengemail前缀的2925邮箱',
                        "cookie": "returnURL=%2Fconnect%2Fauthorize%2Fcallback%3Fclient_id%3DB9257F7F9B1EF15CE%26redirect_uri%3Dhttps%253A%252F%252Fwww.2925.com%252Fauth%252Fsignin-oidc%26response_type%3Dcode%2520id_token%26scope%3Dopenid%2520profile%25202980_client%40web%2520offline_access%26response_mode%3Dform_post%26nonce%3D638838645191247299.NTQzY2NlZTUtODgyYi00NDg2LWE0ZWUtYjE4ZmVlODVkODEzYWVmYWYxOWEtYmNiNy00MGM2LThkMDQtYjRlNzEzZDgzMmZh%26state%3DCfDJ8LvNtK7K79pCgl25c3WvKz1yDKEto8Lm1_YqdifONsH4a6vr55y9tYlCvUV8erOVHoyz0qSl86h98G_1PpUuBXpYfcQvsUuZ887KQyz9HuKz7R-xiapQc-Drxq9YnqWyy9m9aCyem9BkWl0tdAnqh_qwZtd-gMAxFzsu9G7MKyBGPXTXnk1HBeTXqoyNcvkpJFGHY5MHUvaY5J_fDtssBkLKoWpq82Y5GnjRtVCEudHqzses4fxrXGzWbp8LNqh6TD1a5KH_mTll4zAbKbBBzvEFEQ8FTE1RxS9KFjqhcuNCSl1qOWW-Glp_N2WGYpRsZvHqw2ra6BMlGqnWbnaylIp2kBov3o2RjD8hZm2_lttaYOwOawaRAYKrvkQpMO-eoikAVV5OMW089Hi0EuOlM-w%26x-client-SKU%3DID_NETSTANDARD2_0%26x-client-ver%3D5.5.0.0; LoginType=1; .AspNetCore.Cookies=chunks-2; .AspNetCore.CookiesC1=CfDJ8LvNtK7K79pCgl25c3WvKz1fkqwLoJSuq62L_UmkEVCniSLRAbGTsdkrpNEhVgZG34mQ8kfFeUu7KDXt_hPAfoRJ4sHq72bAGflHCgZlWlVHJDXg_54p3C5wXfsO0BFaW-9gRsOUEMgS0ZdUhsFrJ1tvAZdsBlgL4D8LpwZcaprCqP--zefWypcaJlAZlkAdxn7Bk1ETrTAX8uP7fSBm8hqTDDjc-BqQ6SdFdXgjcf3tFVAU-hRYCZNtRyLoCdesy_OKFt0yWWc1O8C69-Yf9xWDHgst3K5FcnDWSRdwrqeKCXNkaSw7JHXzptUtNIYMdb-09ONm-mHK73FmdOVJwwhOJI0bCV03K18AqEZ9J3xsfgUrnanH73Na9aauBUuYroJ-w-zhyNCi-TwQlaotW0ztpbkBTW1z1hDnPz7OXTndzDGeAWXBdJ4k6ZCpo36yHnsJ_uYSjLQg4U5gMuzfXZIBB4gmxqqWdPXS2aZl942oMTehI5b-9gMRsuxU05itqr9WzmIHZ2-U_T-aNG9N-ZOWVRsii0CGF5k3RHOVWfSHdSpgc1Du3LCbOTvD0OGd6yfQQeNPKYhMZYgvNcYzzDb0LkRMgp_wHMI7fNgGNA0gIe99QAYjbMVlvIc7GcvzcrEdRAi4TREsnU_eKkYkMhDw4YAIQ7MAhDG3Cp377U_v2a2B0iXRTrhnL07bLYmVPcKbVexLeXg3i_qtusRco-EVtWO5k60lvKbsxQO43asXT_vF5IQvHPGYCicwg40AoycjfD8nRX2mi9aRjcd5Ztnq0rEoEEcy_i1V2orpEzY_fv78o8kUIfitQrQlTLR-cvLVaP5_awAUSD9-CR59xqpwbgdn92_gIx8xh7batV8FV-jU6rA5ngh0K5NKY4VgLgR_-Jn89aIPV7x4kSPDUOb_7N264G_XKqiaiyi-SDTuUISCoUNq-78rbroriXRJg8b2BwAe1fuC12uXRfvbh9B2jIRNZssOITcShl5vDgfthNQrSwC4zYhrepcC0ZXrbzdb-yQlCPTVpxcDfsO4QNXyYKCclOx5OQCbR3i2DbFvJT_9YvNwyzxTu9XY1eQLz3G3zeHzJbH4X7UspHqFZ14b4_NsWS5pexhQ9QXJyaSxAsTah6H-fcPTILmCvoZjQ6ipgctAogTK2OIi50Jh3fuHbNKjsVlQGNXw8A0RDw0hgnNCpr-vycobGdMM7JQattHElPYzzHHo3sdRjLsPeosFXydCZ4Akmr5Mdpo89WTkjMFuB6B8WzjZQ_FNu4a__F_HIeBbhEzoHvEKDiyyX5DAPXF16ot00VBDVMGAu_0YATrJO8zbYAOY11Tl1JenCAI0KDbVBrXYxD7JDTgTEid4eaDZ_Wa3swGlGQSMd8VyTVqP2JxQQXYKQ3cRui70BPLWaz5T11S76Rb1uaO3shHjto36_MF6JWOTTVsQFGE8txZicor9iHB1NSzYtkEmTaLWRBSdF1QQ_7z30frx1XQaYqTfmpr7apRkNTWdDM_VDJZXfVhvAB6amzE-wGLc1p1FLgmuMxLRGEJ2r_QximEeKP0IZQMuyC5L8ePwMVqdglRr8T2v3Sf99BjBDNdRnpJvXm3Y-PSg_nmW8ffQI9-De9ysl2gb8C3u8dgKoVjtGxjqx7OC4ZlUgeSngPjNkMR2aso_kLQkwHhqqFyojo7T_hBIlxhdXq-COc-wVTtvibyyI98wBZ0FrJwCC0z3QnQb4_-nhmklUmC17HvOnl5SgJ6_K1zTVhsDpXHyJbZL5IbSeM-C_VnyqJzeFOSblVlvOBYBwvq3Hb8tbvGXw00ET4YN3igZ7FzKgj2sLM4Sjm5T5AvbmkOu8XpqyIAAckpnoysWBfNxTsd6JVubWcLIHUgfvEgzX1AjQg5lQR9-OVolB8NkCyVKRSZFNiR4wxBOoBsIPq3fho8o5uYuYt0hFKkS0ukgkPZqRPAI7yxR5-VMkf3T-wk5uurSDp66roYrW5ZgfC4v0ENoKKXpc86heDY-RIVYtQBu1D7-5T7VraQrYZ9E0MNb_J8LcXAVRyaiip78CGrhbl4vEgkoGoBFcM2q85Ce2LgQT4MlVtUOFpOMCQaUjYQO4atWEv5sD4x2yxFIQ5Pg_gkFzKfiWzWuNveIaGuwbIVaQ0e37B6FTwpRUIdc8IZ_Vxt5IeG0KB97qQ46BHHXzl-R6IYKrynP4M9h8pIGU23L6pIpry-XfkoxBfoWi2-N_2WjA26xbC7JLARxS7CaBEAyNuawhO160vOWKn6UOjaaBy1sfVCE_YzznhOTc6eeH1fI1Hdh-5u1SHJxKFznlprUW2OFfN6dpw2Bgr7gU30oXlbJnGwXy8GIY1b81Q1upTZ7gNbt_tbEbG4ql-YK-9Mec_1TlRetQhqgcn1XAbFyGi-a9M-wE4lBGlf_WcMqSt8o8_HKGz0PiIEmOcHPFNE1w1HSB3QIAbztzA0Dtw4Q9xeY-YYWC1vuNaTEOytIlKH6A7nBSN27PTU1dSucwdt_G2VImCC4gDv5pA3j6_JvHJ5z6htA9mY3KnoR4kJmll-X9RQToArSAE2ovmGbA-TXgdOb_YDUt2gjcefistSvSumf6I8w1DoZSkex5HmpZfZIJGStO2INug5Wd_KLDEbNpdZUyfJvvDmYH6t7Ga8nsgTIf2E3Wwv0Imd1j5ACDilO01FUvPqi_vxKcIrstMfBttuxCy9qdJFmVbme5OyMWeVTQNF6ymXvZXnEvkPGcppKWZkApeAAMn8CZ7AFSlQ-ZuQeKP8-3a2DcfbRjEykP5uQl_0T25MDZw0tARfPLWXYIZFaAZ2T4pzaS4aXq-DUOUkScG6jXxibzUQ6CraTg16YoajWRg3ffuPoRZuWjwqCSbAlOqZfsTWe2btsSK-ODZz6geX0zIHwJHwCBfQOeLBZKIqTtIAp1XepaEgd609CpSRVBntgxI8QzdPcKS1DN3u-ahquPepV_4MeR22813bSkG-yFdDjRzRnhFqwTC7CIlaj-D6y46llBWN-HRVw7lKEKIQN3aLo4AGWaTZ2g3AEu64rmeTuRDLbtTV9ManQSGPSQSY-vfEKPSq4ke9tZzTUbqI1ywItv0CEFJcsa1GS9EpxoQsSyEKFzuj6CtQNaWSG2TN9mZqFPerjcUGT8kphLJ5x_U1HCVeTeUVBSAaEqbKitXjdeLwtipJ9qKO4EejK36TzQrQlx1MSlQGx1yVcFkr1EV9k1rgdoRwYd3wBQE61s222bZ-JBO3-hQDfLJ9begl88tkuxwj2POetpAQbzRHpTYhDi1qIBi6n66NtI-BdU-uodcS4fC_FfCb_TEXTscApXimc_W1iFtLnbDnAnsIQER01PieTgdBoR9VUUxoswM-MB3iAps2x7Rasr0ZxD6D9fPacnSnLOZgSlu3_IaKCBHRcdLQBFv7QI1j7k24qCqvYnMTnVKLm2ZgeP066Uo5y5F0RHFYEb2IVoC0Z_b6h9w2vkUrhkBjogL4lwXLSgsdAH4Z8p0Nxr99TWCYi_YjwxKnCoJUJ2ulNB6NcxJJzB_6fwZjAHo3P-X19-blypVzdcNwqNpXOcXvmF_kBAdYGKF7UxQSHbzAKIiMsjSE66kEJcZ0CJMRJyAVhKUV3RACsXg_Z13mkvGAxAQ-0VE5QfFIRTfS9Nn7rlOlaxVvaYTKMrEY1OJl0MG07WVjoj4Pkzt47_FdQiauu1z-AXMFlIbnlX2PJMo3QdDWft2YeZsAqL2Mm2uWOnWhyjroji2QjMlDX8tC6v5znMzeQJewJl42mNQ-JOvtZAdDAV_YY1TfXi_-vGk_T8F5ZhzDrxJKwt5wNXqNJycS4gctuu0N8L69w4MJHn8Nq5JpbMQqYQ97Vq4tvE95SzAvkdHFSbFvfEzSIHo2bDlpTrTGNF9Qv1Bcm4MaRV6QD0zqjvMxn0W8G_qS0QwusKxcl0MvTji6YsfaDha2MT6VUGuvw1IE_dEq6K0SDRVlcyZX9BUcNMs29G; .AspNetCore.CookiesC2=jZM9ABVLnFoCPBiqcP17h0JOR-LkRGDrzqH_xgjpebzShDHVD-LkXUJZlWpAqS1g4T_HTOpAyoU72v6V3jYj6vXvsatQhAJkbns3jRN3wgco4xFgFqbxeU82tEcxYWgSfo7L-UIeYSC9N0BBYwSOElF7ZvUwGJ1A9WgnzMgud856Yb8Y3TE9qmwDtYLbAJns3iXvSmXSUsbiy0Z-3XMtdzRZVa8lVncsc3rqxvuEQ_-KVPhJRiJYeKChofK__6QKF4bzxdE3f6F1P3MQu2LdyVYkWhIfhkrYppxHdbTyNv4ljliRvg2ocU6Mq9sPR9GB7NyGrqDK6yX73eeBgnzAPLEJ9dLjv1-dxEIUqFWvQZZUwMJY3wVf2Zv65q4a_o1UrWhLNosoawUgeBmLalYbYbvYj6Z2ZFoFoERfCpTNGLMJEOsnyT4QRpo1F57YE2yXHu8Jr2cUEy5-DIGQWCRuBYZDWv2b85ZzV0qFAmhZsNI6ltE_UFhXwKpFL-dPSWwvZOCtOeXli2ZSc53tuUbZXB_L89KvaSmHl77aKd8QiwI-JCSrII4Wv6TPIw; auc=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.x3g_IQPAYjpfuX6-M2SLo1p4By03RALJBsEmhbJncpk; aut=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6jv9MQIWFjRiWa-UMcMJ-z4AJhuZszUtDNmrC04ccKE; account=yunfengemail%402925.com; nickname=yunfengemail; uid=ae8d5068-0d04-3a32-9a27-2fff212907b3; ano=undefined; jwt_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.qB1HqxHcK-rdMsU6lKrzfdBtIPBIhZy3XdTq9l9xizk",
                        'token_url': 'https://www.2925.com/mailv2/auth/token',  # 可选，为空时使用基础配置
                        'device_uid': '71fc87a4-7e6b-43a0-8941-cc6e51c4a5cc',  # 可选，为空时使用基础配置
                        'jwt_token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.qB1HqxHcK-rdMsU6lKrzfdBtIPBIhZy3XdTq9l9xizk'
                    }
                }
            }
        }
    
    def set_2925_cookie_for_prefix(self, prefix: str, cookie: str):
        """为指定前缀的2925邮箱设置Cookie"""
        if '2925.com' in self.email_routes and prefix in self.email_routes['2925.com']['prefixes']:
            self.email_routes['2925.com']['prefixes'][prefix]['cookie'] = cookie
            logger.info(f"2925邮箱 {prefix} 前缀的Cookie已设置")
        else:
            logger.error(f"未找到前缀 '{prefix}' 的配置")
    
    def set_2925_config_for_prefix(self, prefix: str, **kwargs):
        """为指定前缀的2925邮箱设置其他配置"""
        if '2925.com' in self.email_routes and prefix in self.email_routes['2925.com']['prefixes']:
            prefix_config = self.email_routes['2925.com']['prefixes'][prefix]
            for key, value in kwargs.items():
                if key in ['token_url', 'device_uid', 'cookie', 'jwt_token']:
                    prefix_config[key] = value
                    logger.info(f"2925邮箱 {prefix} 前缀的 {key} 已更新")
        else:
            logger.error(f"未找到前缀 '{prefix}' 的配置")
    
    def set_2925_cookie(self, cookie: str):
        """设置2925邮箱的Cookie（兼容旧版本，设置给所有前缀）"""
        for prefix in self.email_routes['2925.com']['prefixes']:
            self.set_2925_cookie_for_prefix(prefix, cookie)
        logger.info("所有2925邮箱前缀的Cookie已设置")
    
    def set_2925_config(self, **kwargs):
        """设置2925邮箱API基础配置"""
        for key, value in kwargs.items():
            if key in self.api_2925_base_config:
                self.api_2925_base_config[key] = value
                logger.info(f"2925邮箱基础配置已更新: {key}")
    
    def get_2925_config_for_prefix(self, prefix: str) -> Dict:
        """获取指定前缀的完整2925邮箱配置"""
        if '2925.com' not in self.email_routes or prefix not in self.email_routes['2925.com']['prefixes']:
            raise ValueError(f"未找到前缀 '{prefix}' 的配置")
        
        # 获取前缀特定配置
        prefix_config = self.email_routes['2925.com']['prefixes'][prefix]
        
        # 合并基础配置和前缀特定配置
        config = self.api_2925_base_config.copy()
        
        # 覆盖前缀特定配置
        if prefix_config.get('token_url'):
            config['token_url'] = prefix_config['token_url']
        if prefix_config.get('device_uid'):
            config['device_uid'] = prefix_config['device_uid']
        
        config['cookie'] = prefix_config.get('cookie', '')
        
        return config
    
    def parse_email_for_verification(self, email_address: str) -> Dict:
        """
        解析邮箱并返回验证码获取策略
        
        参数:
        email_address: 要解析的邮箱地址
        
        返回:
        dict: 包含获取策略的字典
        """
        logger.info(f"开始解析邮箱: {email_address}")
        
        # 分离用户名和域名
        if '@' not in email_address:
            raise ValueError(f"无效的邮箱格式: {email_address}")
        
        username, domain = email_address.split('@', 1)
        logger.info(f"解析结果 - 用户名: {username}, 域名: {domain}")
        # ****************************************************
        # 使用 自定义域名时 专用
        route_config = self.email_routes['my-moon.fun']
        result = {
            'type': '163',
            'original_email': email_address,
            'target_email': route_config['target_email'],
            'password': route_config['password'],
            'pop3_server': route_config['pop3_server'],
            'pop3_port': route_config['pop3_port']
        }
        logger.info(f"匹配到163路由: {result}")
        return result
        # ***********************************************************
        
        # # 检查域名路由    使用2925邮箱专用
        # if domain in self.email_routes:
        #     route_config = self.email_routes[domain]
            
        #     if route_config['type'] == '163':
        #         # 163邮箱路由
        #         result = {
        #             'type': '163',
        #             'original_email': email_address,
        #             'target_email': route_config['target_email'],
        #             'password': route_config['password'],
        #             'pop3_server': route_config['pop3_server'],
        #             'pop3_port': route_config['pop3_port']
        #         }
        #         logger.info(f"匹配到163路由: {result}")
        #         return result
                
        #     elif route_config['type'] == '2925':
        #         # 2925邮箱路由 - 需要解析前缀
        #         prefix = self._extract_2925_prefix(username)
        #         if prefix and prefix in route_config['prefixes']:
        #             prefix_config = route_config['prefixes'][prefix]
        #             result = {
        #                 'type': '2925',
        #                 'original_email': email_address,
        #                 'prefix': prefix,
        #                 'api_type': prefix_config['api_type'],
        #                 'description': prefix_config['description']
        #             }
        #             logger.info(f"匹配到2925路由: {result}")
        #             return result
        #         else:
        #             logger.warning(f"2925邮箱前缀 '{prefix}' 未在配置中找到")
        #             # 返回默认的2925配置
        #             result = {
        #                 'type': '2925',
        #                 'original_email': email_address,
        #                 'prefix': prefix or 'unknown',
        #                 'api_type': 'default_2925',
        #                 'description': '未知前缀的2925邮箱'
        #             }
        #             return result
        
        # 如果没有匹配的路由，抛出异常
        raise ValueError(f"未找到邮箱域名 '{domain}' 的路由配置")
    
    def _extract_2925_prefix(self, username: str) -> Optional[str]:
        """
        从2925邮箱用户名中提取前缀
        
        参数:
        username: 邮箱用户名部分
        
        返回:
        str: 提取的前缀，如果无法提取则返回None
        """
        # 定义前缀提取规则
        prefix_patterns = [
            r'^(mailtosun)',      # mailtosun开头
            r'^(mailtoson)', 
            r'^(yunfengemail)',   # yunfengemail开头
        ]
        
        for pattern in prefix_patterns:
            match = re.match(pattern, username)
            if match:
                prefix = match.group(1)
                logger.info(f"提取到2925前缀: {prefix}")
                return prefix
        
        logger.warning(f"无法从用户名 '{username}' 中提取已知前缀")
        return None
    
    def get_verification_code_163(self, original_email: str, route_info: Dict, 
                                max_attempts: int = 30, delay: int = 5) -> Optional[str]:
        """
        使用163邮箱POP3协议获取验证码
        
        参数:
        original_email: 原始邮箱地址
        route_info: 路由信息
        max_attempts: 最大尝试次数
        delay: 每次尝试间隔
        
        返回:
        str: 验证码，如果未找到则返回None
        """
        logger.info(f"使用163邮箱获取验证码，目标邮箱: {original_email}")
        
        target_email = route_info['target_email']
        password = route_info['password']
        pop3_server = route_info['pop3_server']
        pop3_port = route_info['pop3_port']
        
        for attempt in range(max_attempts):
            logger.info(f"第 {attempt+1}/{max_attempts} 次尝试获取验证码...")
            
            try:
                # 连接到POP3服务器
                pop_conn = poplib.POP3_SSL(pop3_server, pop3_port)
                
                # 身份验证
                pop_conn.user(target_email)
                login_response = pop_conn.pass_(password)
                logger.info(f"163邮箱登录响应: {login_response}")
                
                # 获取邮件统计信息
                stat_response = pop_conn.stat()
                logger.info(f"邮件统计: 邮件数量 = {stat_response[0]}")
                
                if stat_response[0] == 0:
                    logger.info("163邮箱中没有邮件")
                    pop_conn.quit()
                    if attempt < max_attempts - 1:
                        logger.info(f"等待 {delay} 秒后重试...")
                        time.sleep(delay)
                    continue
                
                # 获取最新的几封邮件
                resp, mails, octets = pop_conn.list()
                mail_indices = [int(mail.split()[0]) for mail in mails]
                mail_indices.sort(reverse=True)  # 最新邮件优先
                
                # 检查最新的10封邮件
                for mail_index in mail_indices[:10]:
                    response, lines, octets = pop_conn.retr(mail_index)
                    message_content = b'\n'.join(lines)
                    msg = email.message_from_bytes(message_content)
                    
                    # 提取邮件信息
                    subject = msg.get('subject', '')
                    to_addr = msg.get('to', '')
                    
                    # 提取邮件正文
                    body = self._extract_email_body(msg)
                    
                    # 检查是否是目标邮件
                    if self._is_target_verification_email(original_email, to_addr, subject, body):
                        # 提取验证码
                        verification_code = self._extract_verification_code(body)
                        if verification_code:
                            logger.info(f"从163邮箱成功获取验证码: {verification_code}")
                            pop_conn.quit()
                            return verification_code
                
                pop_conn.quit()
                
            except Exception as e:
                logger.error(f"163邮箱获取验证码时出错: {str(e)}")
                try:
                    pop_conn.quit()
                except:
                    pass
            
            if attempt < max_attempts - 1:
                logger.info(f"等待 {delay} 秒后重试...")
                time.sleep(delay)
        
        logger.error("163邮箱获取验证码失败，超过最大尝试次数")
        return None
    
    def get_verification_code_2925(self, original_email: str, route_info: Dict, 
                                  max_attempts: int = 30, delay: int = 5) -> Optional[str]:
        """
        使用2925邮箱API获取验证码
        参照get_mail_code.py的完整实现，添加重试机制
        
        参数:
        original_email: 原始邮箱地址
        route_info: 路由信息，包含prefix等信息
        max_attempts: 最大尝试次数，默认30次
        delay: 每次尝试间隔，默认5秒
        
        返回:
        str: 验证码，如果未找到则返回None
        """
        logger.info(f"使用2925邮箱API获取验证码，邮箱: {original_email}")
        
        # 获取前缀
        prefix = route_info.get('prefix')
        if not prefix:
            logger.error("2925邮箱路由信息中缺少prefix")
            return None
        
        try:
            # 获取前缀特定的配置
            config = self.get_2925_config_for_prefix(prefix)
            
            # 检查Cookie是否已设置
            cookie_value = config.get('cookie')
            jwt_token = config.get('jwt_token')
            if not cookie_value:
                logger.error(f"2925邮箱 {prefix} 前缀的Cookie未设置，请先调用set_2925_cookie_for_prefix方法")
                return None
            
            # 1. 获取JWT token
            cookie_value, jwt_token = self._get_jwt_token_2925(cookie_value, config)
            if not jwt_token:
                logger.error("JWT token获取失败，请检查cookie是否有效")
                return None
            
            logger.info(f"开始尝试获取邮件，最大尝试次数: {max_attempts}, 间隔: {delay}秒")
            
            # 2. 循环尝试获取邮件
            for attempt in range(max_attempts):
                logger.info(f"第 {attempt+1}/{max_attempts} 次尝试获取邮件...")
                
                # 获取邮件列表
                mail_list_response = self._get_mail_list_2925(cookie_value, jwt_token, original_email, config)
                
                if not (mail_list_response and mail_list_response.get("code") == 200):
                    logger.warning(f"第 {attempt+1} 次获取邮件列表失败。响应: {mail_list_response}")
                    
                    # 如果是401错误，尝试重新获取token
                    if mail_list_response and mail_list_response.get("code") == 401:
                        logger.info("Token可能已过期，重新获取...")
                        cookie_value, jwt_token = self._get_jwt_token_2925(cookie_value, config)
                        if not jwt_token:
                            logger.error("重新获取JWT token失败")
                            return None
                        continue
                    
                    if attempt < max_attempts - 1:
                        logger.info(f"等待 {delay} 秒后重试...")
                        time.sleep(delay)
                    continue
                
                # 3. 查找匹配的邮件
                found_mail = None
                if mail_list_response.get("result") and mail_list_response["result"].get("list"):
                    mail_list = mail_list_response["result"]["list"]
                    logger.info(f"获取到 {len(mail_list)} 封邮件，开始查找匹配邮件...")
                    
                    for mail_item in mail_list:
                        # 检查收件人地址是否匹配
                        to_addresses = mail_item.get("toAddress", [])
                        logger.debug(f"邮件收件人: {to_addresses}")
                        
                        if original_email in to_addresses:
                            found_mail = mail_item
                            logger.info(f"找到匹配邮件: 发件人={mail_item.get('fromAddress')}, 主题={mail_item.get('subject', '')[:50]}...")
                            break
                else:
                    logger.info(f"第 {attempt+1} 次尝试：邮件列表为空或格式异常")
                
                if not found_mail:
                    logger.info(f"第 {attempt+1} 次尝试：未在邮件列表中找到发给 '{original_email}' 的邮件")
                    if attempt < max_attempts - 1:
                        logger.info(f"等待 {delay} 秒后重试...")
                        time.sleep(delay)
                    continue
                
                # 4. 读取邮件内容
                message_id = found_mail.get("messageId")
                logger.info(f"找到匹配邮件: MessageID = {message_id}")
                
                mail_content_response = self._read_mail_2925(message_id, cookie_value, jwt_token, original_email, config)
                if not (mail_content_response and mail_content_response.get("code") == 200):
                    logger.error(f"读取邮件内容失败。响应: {mail_content_response}")
                    
                    # 如果是401错误，尝试重新获取token
                    if mail_content_response and mail_content_response.get("code") == 401:
                        logger.info("读取邮件时Token可能已过期，重新获取...")
                        cookie_value, jwt_token = self._get_jwt_token_2925(cookie_value, config)
                        if not jwt_token:
                            logger.error("重新获取JWT token失败")
                            return None
                        continue
                    
                    if attempt < max_attempts - 1:
                        logger.info(f"读取邮件失败，等待 {delay} 秒后重试...")
                        time.sleep(delay)
                    continue
                
                logger.info("成功读取邮件内容")
                
                # 5. 提取验证码
                body_text = mail_content_response.get("result", {}).get("bodyText")
                if not body_text:
                    logger.error("邮件内容中未找到bodyText")
                    if attempt < max_attempts - 1:
                        logger.info(f"邮件内容异常，等待 {delay} 秒后重试...")
                        time.sleep(delay)
                    continue
                
                verification_code = self._extract_verification_code_2925(body_text)
                if verification_code:
                    logger.info(f"从2925邮箱成功获取验证码: {verification_code}")
                    return verification_code
                else:
                    logger.warning("在邮件正文中未能提取到验证码")
                    if attempt < max_attempts - 1:
                        logger.info(f"验证码提取失败，等待 {delay} 秒后重试...")
                        time.sleep(delay)
                    continue
            
            # 如果所有尝试都失败了
            logger.error(f"2925邮箱获取验证码失败，已尝试 {max_attempts} 次")
            return None
                
        except Exception as e:
            logger.error(f"2925邮箱获取验证码时出错: {str(e)}")
            return None
    
    def _get_jwt_token_2925(self, cookie_value: str, config: Dict) -> Tuple[str, Optional[str]]:
        """
        获取2925邮箱的JWT token
        
        参数:
        cookie_value: Cookie值
        config: 2925邮箱配置
        
        返回:
        tuple: (更新后的cookie字符串, 新的JWT token)
        """
        url = config['token_url']
        # headers = config['headers'].copy()
        headers = {
            "accept": "application/json, text/plain, */*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9",
            "content-length": "16",
            "content-type": "application/json",
            "origin": "https://www.2925.com",
            "priority": "u=1, i",
            "referer": "https://www.2925.com/",
            "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "cookie": cookie_value

            # "cache-control": "no-cache",
            # "pragma": "no-cache",
            # "priority": "u=1, i",
            # "sec-ch-ua": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            # "sec-ch-ua-mobile": "?0",
            # "sec-ch-ua-platform": '"macOS"',
            # "sec-fetch-dest": "empty",
            # "sec-fetch-mode": "cors",
            # "sec-fetch-site": "same-origin", 
            # "x-requested-with": "XMLHttpRequest",
            # "Referer": "https://2925.com/",
            # "Referrer-Policy": "strict-origin-when-cross-origin",
            # "cookie": my_cookie,
            # "authorization": f"Bearer {my_jwt_token}",
            # "deviceuid": my_device_uid,
        }
        try:
            print('正式请求')
            time.sleep(3)
            response = requests.post(url, headers=headers, data= {"timeout":5000})
            
            if response.status_code == 200:
                try:
                    json_resp = response.json()
                    if json_resp.get("code") == 200 and json_resp.get("result"):
                        new_token = json_resp.get("result")
                    else:
                        new_token = response.text.strip('\"')
                except json.JSONDecodeError:
                    new_token = response.text.strip('\"')
                
                # 更新cookie中的jwt_token
                if "jwt_token=" in cookie_value:
                    updated_cookie = re.sub(r'jwt_token=[^;]+', f'jwt_token={new_token}', cookie_value)
                else:
                    updated_cookie = cookie_value + f"; jwt_token={new_token}"
                
                logger.info("JWT token已更新")
                return updated_cookie, new_token
            else:
                logger.error(f"获取JWT token失败，状态码: {response.status_code}")
                return cookie_value, None
        except Exception as e:
            logger.error(f"获取JWT token时发生错误: {str(e)}")
            return cookie_value, None
    
    def _get_mail_list_2925(self, cookie_value: str, jwt_token: str, mail_box: str, config: Dict, retry: bool = True) -> Dict:
        """
        获取2925邮箱的邮件列表
        
        参数:
        cookie_value: Cookie值
        jwt_token: JWT token
        mail_box: 邮箱地址
        config: 2925邮箱配置
        retry: 是否在401错误时重试
        
        返回:
        dict: 邮件列表数据
        """
        url = config['mail_list_url']
        
        params = {
            "Folder": "Inbox",
            "MailBox": mail_box,
            "FilterType": "0",
            "PageIndex": "1",
            "PageCount": "25",
            "traceId": ""
        }

        prefix = self._extract_2925_prefix(mail_box)
        my_cookie = self.email_routes['2925.com']['prefixes'][prefix]['cookie']
        my_jwt_token = self.email_routes['2925.com']['prefixes'][prefix]['jwt_token']
        my_device_uid = self.email_routes['2925.com']['prefixes'][prefix]['device_uid']
    
        headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "cache-control": "no-cache",
            # "deviceuid": "5fce6dfe-5898-4ec4-a30e-9eadd7ef754f",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "sec-ch-ua": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"macOS"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin", 
            "x-requested-with": "XMLHttpRequest",
            "Referer": "https://2925.com/",
        # "authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.k19q3PgPgDbyaizdPcUl-oVxfvdXmSUsFYOND9kR9vE",

        # "cookie": "auc=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyZWZyZXNoX2FnZW50IjoiMS95b1pXeCtPNHNLaUpBbjZJUGFpM21uME9LYVpPeUJ2Z2tJazQ2Ym5nMD0iLCJncmFudF90eXBlIjoiV2ViQ2xpZW50IiwiY2xpZW50X2lkIjoiQjkyNTdGN0Y5QjFFRjE1Q0UiLCJyZXFJZCI6ImU1NTU5OGVmLWY4OWQtNDQxZi1iMjdjLWVmYjMzODMyMTg5OCIsImF1ZCI6IkI5MjU3RjdGOUIxRUYxNUNFIiwic3ViIjoiYWU4ZDUwNjgtMGQwNC0zYTMyLTlhMjctMmZmZjIxMjkwN2IzIiwianRpIjoiYWU4ZDUwNjgtMGQwNC0zYTMyLTlhMjctMmZmZjIxMjkwN2IzIiwiaWF0IjoxNzQ5MzkwMjE2LCJpc3MiOiJodHRwczovL21haWxsb2dpbi4yOTgwLmNvbS9vYXV0aCIsImV4cCI6MTc1MTk4MjIxNiwibmJmIjoxNzQ5MzkwMjE2fQ.vlZdu7fmqwxJt4Z5uYdNX-RWFfBckDnyqIGZT1F1j6Q; aut=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.k19q3PgPgDbyaizdPcUl-oVxfvdXmSUsFYOND9kR9vE; jwt_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.k19q3PgPgDbyaizdPcUl-oVxfvdXmSUsFYOND9kR9vE; account=yunfengemail%402925.com; nickname=yunfengemail; uid=ae8d5068-0d04-3a32-9a27-2fff212907b3; ano=undefined",

            "Referrer-Policy": "strict-origin-when-cross-origin",
            "cookie": cookie_value,
            "authorization": f"Bearer {jwt_token}",
            "deviceuid": my_device_uid,

            
        }
        
        try:
            response = requests.get(url, params=params, headers=headers)
            
            if response.status_code == 200:
                try:
                    return response.json()
                except json.JSONDecodeError:
                    logger.error("解析邮件列表响应JSON失败")
                    return {"code": 400, "message": response.text}
            elif response.status_code == 401 and retry:
                logger.info("Token无效，正在重新获取...")
                cookie_value, new_token = self._get_jwt_token_2925(cookie_value, config)
                if new_token:
                    return self._get_mail_list_2925(cookie_value, new_token, mail_box, config, False)
                else:
                    return {"code": 401, "message": "重新获取token失败"}
            else:
                logger.error(f"获取邮件列表失败，状态码: {response.status_code}")
                return {"code": response.status_code, "message": response.text}
        except Exception as e:
            logger.error(f"获取邮件列表时发生错误: {str(e)}")
            return {"code": 500, "message": str(e)}
    
    def _read_mail_2925(self, message_id: str, cookie_value: str, jwt_token: str, mail_box: str, config: Dict, retry: bool = True) -> Dict:
        """
        读取2925邮箱的特定邮件内容
        
        参数:
        message_id: 邮件ID
        cookie_value: Cookie值
        jwt_token: JWT token
        mail_box: 邮箱地址
        config: 2925邮箱配置
        retry: 是否在401错误时重试
        
        返回:
        dict: 邮件内容数据
        """
        url = config['mail_read_url']
        
        params = {
            "MessageID": message_id,
            "FolderName": "Inbox",
            "MailBox": mail_box,
            "IsPre": "false",
            "traceId": "14cfa9c2d50e"
        }
        
        headers = config['headers'].copy()
        headers.update({
            "authorization": f"Bearer {jwt_token}",
            "deviceuid": config['device_uid'],
            "cookie": cookie_value
        })
        
        try:
            response = requests.get(url, params=params, headers=headers)
            
            if response.status_code == 200:
                try:
                    return response.json()
                except json.JSONDecodeError:
                    logger.error("解析邮件内容响应JSON失败")
                    return {"code": 400, "message": response.text}
            elif response.status_code == 401 and retry:
                logger.info("Token无效，正在重新获取...")
                cookie_value, new_token = self._get_jwt_token_2925(cookie_value, config)
                if new_token:
                    return self._read_mail_2925(message_id, cookie_value, new_token, mail_box, config, False)
                else:
                    return {"code": 401, "message": "重新获取token失败"}
            else:
                logger.error(f"读取邮件内容失败，状态码: {response.status_code}")
                return {"code": response.status_code, "message": response.text}
        except Exception as e:
            logger.error(f"读取邮件内容时发生错误: {str(e)}")
            return {"code": 500, "message": str(e)}
    
    def _extract_verification_code_2925(self, body_text: str) -> Optional[str]:
        """
        从2925邮箱的邮件正文中提取验证码
        参照get_mail_code.py中的提取逻辑
        
        参数:
        body_text: 邮件正文
        
        返回:
        str: 验证码，如果未找到则返回None
        """
        # 方法1: 查找带空格的数字行 (如 "1 3 0 8 5 9")
        code_lines = [line.strip() for line in body_text.splitlines() 
                     if line.strip().replace(" ", "").isdigit() 
                     and len(line.strip().replace(" ", "")) > 3]
        
        if code_lines:
            # 尝试从包含多个数字的行中提取，例如 "1 3 0 8 5 9"
            for line in code_lines:
                if " " in line and len(line.split()) > 3:  # 检查是否有空格且数字个数大于3
                    verification_code = line
                    logger.info(f"通过方法1找到验证码: {verification_code}")
                    return verification_code
        
        # 方法2: 查找纯数字行，通常在"This code expires"之前的位置
        for line in body_text.splitlines():
            line = line.strip()
            if line.isdigit() and len(line) >= 6:  # 验证码通常是6位或更长
                verification_code = line
                logger.info(f"通过方法2找到验证码: {verification_code}")
                return verification_code
        
        # 方法3: 使用正则表达式匹配特定模式
        # 匹配"Enter the code below"之后的纯数字
        enter_code_pattern = re.search(r'Enter the code below.*?\n\s*(\d{6,})', body_text, re.DOTALL)
        if enter_code_pattern:
            verification_code = enter_code_pattern.group(1)
            logger.info(f"通过正则表达式匹配'Enter the code below'后的数字找到验证码: {verification_code}")
            return verification_code
        
        # 匹配两个空行之间的纯数字
        between_lines_pattern = re.search(r'\n\s*\n\s*(\d{6,})\s*\n\s*\n', body_text)
        if between_lines_pattern:
            verification_code = between_lines_pattern.group(1)
            logger.info(f"通过正则表达式匹配空行之间的数字找到验证码: {verification_code}")
            return verification_code
        
        logger.warning(f"在邮件正文中未能按预期格式找到验证码。邮件正文: \n{body_text}")
        return None
    
    def get_verification_code_smart(self, email_address: str, max_attempts: int = 15, 
                                  delay: int = 5) -> Optional[str]:
        """
        智能获取验证码 - 根据邮箱自动选择获取方式
        
        参数:
        email_address: 邮箱地址
        max_attempts: 最大尝试次数（仅对163有效）
        delay: 重试间隔（仅对163有效）
        
        返回:
        str: 验证码，如果未找到则返回None
        """
        logger.info(f"开始智能获取验证码: {email_address}")
        
        try:
            # 1. 解析邮箱获取路由信息
            route_info = self.parse_email_for_verification(email_address)
            
            # 2. 根据类型调用对应的获取方法

            if route_info['type'] == '2925':
                return self.get_verification_code_2925(email_address, route_info, max_attempts, delay)
            else :
                # 其他都为 163
                route_info2 = {
                'type': '163',
                'target_email': '<EMAIL>',
                'password': 'CWvE9mncY6MKPFxW',
                'pop3_server': 'pop.163.com',
                'pop3_port': 995
            }
                return self.get_verification_code_163(email_address, route_info2, max_attempts, delay)
        except Exception as e:
            logger.error(f"智能获取验证码失败: {str(e)}")
            return None
    
    def _extract_email_body(self, msg) -> str:
        """提取邮件正文"""
        body = ""
        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))
                
                if "attachment" in content_disposition:
                    continue
                
                if content_type == "text/plain" and "attachment" not in content_disposition:
                    try:
                        part_body = part.get_payload(decode=True)
                        encodings = ['utf-8', 'gb2312', 'gbk', 'gb18030', 'big5']
                        for enc in encodings:
                            try:
                                body = part_body.decode(enc)
                                break
                            except UnicodeDecodeError:
                                continue
                        if not body:
                            body = part_body.decode('utf-8', errors='replace')
                        break
                    except Exception as e:
                        logger.error(f"解码邮件部分时出错: {str(e)}")
        else:
            try:
                part_body = msg.get_payload(decode=True)
                encodings = ['utf-8', 'gb2312', 'gbk', 'gb18030', 'big5']
                for enc in encodings:
                    try:
                        body = part_body.decode(enc)
                        break
                    except UnicodeDecodeError:
                        continue
                if not body:
                    body = part_body.decode('utf-8', errors='replace')
            except Exception as e:
                logger.error(f"解码邮件正文时出错: {str(e)}")
        
        return body
    
    def _is_target_verification_email(self, target_email: str, to_addr: str, 
                                    subject: str, body: str) -> bool:
        """判断是否是目标验证邮件"""
        # 检查收件人是否匹配
        if target_email == to_addr:
            return True
        
        # 检查邮件内容是否包含目标邮箱和Cursor相关信息
        if "Cursor" in subject and target_email in body:
            return True
        
        return False
    
    def _extract_verification_code(self, body: str) -> Optional[str]:
        """从邮件正文中提取验证码"""
        # 1. 标准6位连续数字
        code_match = re.search(r'\b\d{6}\b', body)
        if code_match:
            return code_match.group(0)
        
        # 2. 带空格的6位数字
        spaced_code_match = re.search(r'[\d\s]{9,17}', body)
        if spaced_code_match:
            spaced_code = spaced_code_match.group(0).strip()
            cleaned_code = ''.join(filter(str.isdigit, spaced_code))
            if len(cleaned_code) == 6:
                return cleaned_code
        
        # 3. 上下文中的验证码
        code_context_match = re.search(r'code\s+is:?\s*[\'"]?(\d[\d\s]+\d)[\'"]?', body, re.IGNORECASE)
        if code_context_match:
            context_code = code_context_match.group(1).strip()
            cleaned_code = ''.join(filter(str.isdigit, context_code))
            if len(cleaned_code) == 6:
                return cleaned_code
        
        return None
    
    def add_email_route(self, domain: str, route_config: Dict):
        """
        添加新的邮箱路由配置
        
        参数:
        domain: 邮箱域名
        route_config: 路由配置
        """
        self.email_routes[domain] = route_config
        logger.info(f"添加邮箱路由: {domain} -> {route_config}")
    
    def get_supported_domains(self) -> list:
        """获取支持的邮箱域名列表"""
        return list(self.email_routes.keys())


# 创建全局实例
smart_verifier = SmartEmailVerification()

def get_verification_code_smart(email_address: str, max_attempts: int = 30, 
                              delay: int = 5) -> Optional[str]:
    """
    智能获取验证码的便捷函数
    
    参数:
    email_address: 邮箱地址
    max_attempts: 最大尝试次数
    delay: 重试间隔
    
    返回:
    str: 验证码，如果未找到则返回None
    """
    return smart_verifier.get_verification_code_smart(email_address, max_attempts, delay)


# 测试函数
def test_email_parsing():
    """测试邮箱解析功能"""
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    print("=" * 60)
    print("邮箱解析测试")
    print("=" * 60)
    
    for email_addr in test_emails:
        print(f"\n测试邮箱: {email_addr}")
        try:
            result = smart_verifier.parse_email_for_verification(email_addr)
            print(f"解析结果: {result}")
        except Exception as e:
            print(f"解析失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("支持的域名:", smart_verifier.get_supported_domains())


def test_2925_configuration():
    """测试2925邮箱配置功能"""
    print("\n" + "=" * 60)
    print("2925邮箱配置测试")
    print("=" * 60)
    
    # 示例：为不同前缀设置不同的Cookie
    mailtosun_cookie = "cookie_for_mailtosun_accounts"
    yunfengemail_cookie = "cookie_for_yunfengemail_accounts"
    
    smart_verifier.set_2925_cookie_for_prefix("mailtosun", mailtosun_cookie)
    smart_verifier.set_2925_cookie_for_prefix("yunfengemail", yunfengemail_cookie)
    print("不同前缀的Cookie已分别设置")
    
    # 示例：为不同前缀设置不同的配置
    smart_verifier.set_2925_config_for_prefix("mailtosun",
        device_uid="mailtosun-device-id",
        token_url="https://2925.com/api/auth/token?traceId=mailtosun_trace"
    )
    
    smart_verifier.set_2925_config_for_prefix("yunfengemail",
        device_uid="yunfengemail-device-id", 
        token_url="https://2925.com/api/auth/token?traceId=yunfengemail_trace"
    )
    print("不同前缀的配置已分别设置")
    
    # 示例：设置基础配置（影响所有前缀）
    smart_verifier.set_2925_config(
        mail_list_url="https://2925.com/mailv2/maildata/MailList/mails"
    )
    print("基础配置已更新")
    
    # 显示配置信息
    try:
        mailtosun_config = smart_verifier.get_2925_config_for_prefix("mailtosun")
        yunfengemail_config = smart_verifier.get_2925_config_for_prefix("yunfengemail")
        
        print(f"\nmailtosun前缀配置:")
        print(f"  Cookie: {mailtosun_config['cookie'][:20]}...")
        print(f"  Device UID: {mailtosun_config['device_uid']}")
        print(f"  Token URL: {mailtosun_config['token_url']}")
        
        print(f"\nyunfengemail前缀配置:")
        print(f"  Cookie: {yunfengemail_config['cookie'][:20]}...")
        print(f"  Device UID: {yunfengemail_config['device_uid']}")
        print(f"  Token URL: {yunfengemail_config['token_url']}")
        
    except Exception as e:
        print(f"获取配置时出错: {e}")


if __name__ == "__main__":

    verification_code = get_verification_code_smart('<EMAIL>', max_attempts=15, delay=5)
    # # 运行测试
    # test_email_parsing()
    # test_2925_configuration()
    
    # # 示例使用
    # print("\n" + "=" * 60)
    # print("使用示例")
    # print("=" * 60)
    
    # # 配置2925邮箱Cookie（实际使用时需要真实的Cookie）
    # mailtosun_cookie = ""  # 用户需要在这里填写mailtosun的真实Cookie
    # yunfengemail_cookie = ""  # 用户需要在这里填写yunfengemail的真实Cookie
    
    # if mailtosun_cookie and yunfengemail_cookie:
    #     # 分别配置不同前缀的Cookie
    #     smart_verifier.set_2925_cookie_for_prefix("mailtosun", mailtosun_cookie)
    #     smart_verifier.set_2925_cookie_for_prefix("yunfengemail", yunfengemail_cookie)
        
    #     # 可选：为不同前缀设置不同的配置
    #     smart_verifier.set_2925_config_for_prefix("mailtosun", 
    #         device_uid="mailtosun-custom-device")
    #     smart_verifier.set_2925_config_for_prefix("yunfengemail", 
    #         device_uid="yunfengemail-custom-device")
        
    #     # 示例1: 163邮箱
    #     # code = get_verification_code_smart("<EMAIL>")
    #     # print(f"163邮箱验证码: {code}")
        
    #     # 示例2: mailtosun前缀的2925邮箱
    #     # code = get_verification_code_smart("<EMAIL>")
    #     # print(f"mailtosun邮箱验证码: {code}")
        
    #     # 示例3: yunfengemail前缀的2925邮箱
    #     # code = get_verification_code_smart("<EMAIL>")
    #     # print(f"yunfengemail邮箱验证码: {code}")
    # else:
    #     print("请在代码中设置不同前缀的Cookie以测试2925邮箱功能")
    #     print("示例：")
    #     print("  smart_verifier.set_2925_cookie_for_prefix('mailtosun', 'mailtosun_cookie')")
    #     print("  smart_verifier.set_2925_cookie_for_prefix('yunfengemail', 'yunfengemail_cookie')")
    #     print("然后调用：get_verification_code_smart('<EMAIL>')")
    
    # print("\n配置说明：")
    # print("1. 163邮箱：使用POP3协议，配置在email_routes中")
    # print("2. 2925邮箱：使用API方式，支持按前缀分别配置")
    # print("   - mailtosun前缀：调用 set_2925_cookie_for_prefix('mailtosun', cookie)")
    # print("   - yunfengemail前缀：调用 set_2925_cookie_for_prefix('yunfengemail', cookie)")
    # print("   - 通用配置：调用 set_2925_config(**config) 设置基础配置")
    # print("   - 前缀特定配置：调用 set_2925_config_for_prefix(prefix, **config)")
    # print("3. 支持的域名：", smart_verifier.get_supported_domains())
    # print("\n新增方法：")
    # print("  - set_2925_cookie_for_prefix(prefix, cookie): 为指定前缀设置Cookie")
    # print("  - set_2925_config_for_prefix(prefix, **kwargs): 为指定前缀设置配置")
    # print("  - get_2925_config_for_prefix(prefix): 获取指定前缀的完整配置") 