# 编写一个批量发送邮件的程序
# 发送方163邮箱，接收方任意邮箱
# 邮箱模版 美观大方

import smtplib
import csv
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.image import MIMEImage
from email.mime.application import MIMEApplication
from email.header import Header
from email.utils import formataddr
import time
import os
from datetime import datetime
import sqlite3
import base64
import mimetypes

# 数据库相关函数
def init_database():
    """
    初始化数据库，创建邮箱表
    """
    conn = sqlite3.connect('cursor_auth_store.db')
    cursor = conn.cursor()
    
    # 创建邮箱表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS emails (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT NOT NULL UNIQUE,
        tag TEXT,
        is_sent INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    conn.commit()
    conn.close()
    print("数据库初始化成功")

def add_email(email, tag=""):
    """
    添加邮箱到数据库
    :param email: 邮箱地址
    :param tag: 邮箱标签
    :return: 是否添加成功
    """
    try:
        conn = sqlite3.connect('cursor_auth_store.db')
        cursor = conn.cursor()
        
        # 检查邮箱是否已存在
        cursor.execute("SELECT id FROM emails WHERE email = ?", (email,))
        if cursor.fetchone():
            print(f"邮箱 {email} 已存在，跳过添加")
            conn.close()
            return False
            
        # 添加邮箱
        cursor.execute(
            "INSERT INTO emails (email, tag, created_at, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)",
            (email, tag)
        )
        
        conn.commit()
        conn.close()
        print(f"邮箱 {email} 添加成功")
        return True
        
    except Exception as e:
        print(f"添加邮箱失败: {str(e)}")
        return False

def mark_email_sent(email):
    """
    标记邮箱为已发送状态
    :param email: 邮箱地址
    :return: 是否标记成功
    """
    try:
        conn = sqlite3.connect('cursor_auth_store.db')
        cursor = conn.cursor()
        
        cursor.execute(
            "UPDATE emails SET is_sent = 1, updated_at = CURRENT_TIMESTAMP WHERE email = ?",
            (email,)
        )
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"标记邮箱状态失败: {str(e)}")
        return False

def get_unsent_emails():
    """
    获取所有未发送的邮箱
    :return: 未发送的邮箱列表 [(email, tag), ...]
    """
    try:
        conn = sqlite3.connect('cursor_auth_store.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT email, tag FROM emails WHERE is_sent = 0")
        emails = [(row['email'], row['tag']) for row in cursor.fetchall()]
        
        conn.close()
        return emails
        
    except Exception as e:
        print(f"获取未发送邮箱失败: {str(e)}")
        return []

def import_emails_from_csv(csv_file):
    """
    从CSV文件导入邮箱到数据库
    :param csv_file: CSV文件路径
    :return: 导入成功的邮箱数量
    """
    success_count = 0
    
    try:
        if not os.path.exists(csv_file):
            print(f"错误: 找不到CSV文件 '{csv_file}'")
            return success_count
            
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            try:
                next(reader)  # 跳过表头
            except StopIteration:
                print("CSV文件为空或格式不正确")
                return success_count
                
            for row in reader:
                if len(row) < 2:
                    print(f"跳过无效行: {row}")
                    continue
                    
                name = row[0].strip()
                email = row[1].strip()
                tag = name  # 使用姓名作为标签
                
                # 检查邮箱格式是否有效
                if '@' not in email or '.' not in email:
                    print(f"跳过无效邮箱: {email}")
                    continue
                
                if add_email(email, tag):
                    success_count += 1
    
    except Exception as e:
        print(f"导入邮箱时出错: {str(e)}")
    
    print(f"\n导入完成! 成功导入: {success_count} 个邮箱")
    return success_count

class EmailSender:
    def __init__(self, sender_email, sender_password, smtp_server="smtp.163.com", smtp_port=25):
        """
        初始化邮件发送器
        :param sender_email: 发件人邮箱
        :param sender_password: 发件人邮箱授权码(不是登录密码)
        :param smtp_server: SMTP服务器地址
        :param smtp_port: SMTP服务器端口
        """
        self.sender_email = sender_email
        self.sender_password = sender_password
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.sender_name = "通知"  # 发件人显示名称

    def create_html_template(self, recipient_name, subject, content, embedded_images=None):
        """
        创建HTML邮件模板
        :param recipient_name: 收件人姓名
        :param subject: 邮件主题
        :param content: 邮件内容
        :param embedded_images: 嵌入图片列表 [{'cid': 'img1', 'path': 'path/to/img.jpg'}, ...]
        :return: HTML邮件内容
        """
        current_date = datetime.now().strftime("%Y年%m月%d日")
        
        # 处理内容中的嵌入图片
        if embedded_images:
            # 在此处添加图片引用的HTML代码
            image_html = ""
            for img in embedded_images:
                image_html += f'<div style="text-align: center; margin: 20px 0;"><img src="cid:{img["cid"]}" style="max-width: 100%; height: auto;"/></div>'
            
            # 将图片HTML添加到内容末尾或适当位置
            content =  image_html
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 650px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                .header {{
                    background-color: #4285f4;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    border-radius: 5px 5px 0 0;
                }}
                .content {{
                    background-color: #f9f9f9;
                    padding: 20px;
                    border-left: 1px solid #e0e0e0;
                    border-right: 1px solid #e0e0e0;
                }}
                .footer {{
                    background-color: #f1f1f1;
                    padding: 15px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    border-radius: 0 0 5px 5px;
                    border: 1px solid #e0e0e0;
                }}
                .greeting {{
                    font-weight: bold;
                    margin-bottom: 20px;
                }}
                .signature {{
                    margin-top: 30px;
                    font-style: italic;
                }}
                .date {{
                    text-align: right;
                    margin-bottom: 20px;
                    color: #666;
                }}
                img {{
                    max-width: 100%;
                    height: auto;
                }}
            </style>
        </head>
        <body>
            <div class="content">
                <p class="greeting">尊敬的 用户：</p>
                <p>{content}</p>
                <div class="signature">
                    <p>此致</p>
                </div>
            </div>
            <div class="footer">
                <p>此邮件由系统自动发送，请勿直接回复</p>
                <p>© {datetime.now().year} 版权所有</p>
            </div>
        </body>
        </html>
        """
        return html

    def add_attachment(self, msg, file_path):
        """
        添加附件到邮件
        :param msg: 邮件对象
        :param file_path: 附件文件路径
        """
        if not os.path.exists(file_path):
            print(f"附件文件不存在: {file_path}")
            return
        
        # 获取文件名和MIME类型
        filename = os.path.basename(file_path)
        mime_type, _ = mimetypes.guess_type(file_path)
        
        # 读取文件
        with open(file_path, 'rb') as f:
            attachment_data = f.read()
        
        # 判断是否为图片
        if mime_type and mime_type.startswith('image/'):
            # 图片附件
            attachment = MIMEImage(attachment_data, _subtype=mime_type.split('/')[-1])
        else:
            # 一般附件
            attachment = MIMEApplication(attachment_data)
        
        # 设置附件信息
        attachment.add_header('Content-Disposition', 'attachment', filename=filename)
        msg.attach(attachment)
        print(f"已添加附件: {filename}")

    def add_embedded_image(self, msg, cid, image_path):
        """
        添加嵌入图片到邮件
        :param msg: 邮件对象
        :param cid: 图片在HTML中的引用ID
        :param image_path: 图片文件路径
        """
        if not os.path.exists(image_path):
            print(f"图片文件不存在: {image_path}")
            return False
        
        # 获取图片类型
        mime_type, _ = mimetypes.guess_type(image_path)
        if mime_type and mime_type.startswith('image/'):
            subtype = mime_type.split('/')[-1]
        else:
            print(f"不支持的图片类型: {image_path}")
            return False
        
        # 读取图片数据
        with open(image_path, 'rb') as img_file:
            img_data = img_file.read()
        
        # 创建图片对象
        image = MIMEImage(img_data, _subtype=subtype)
        image.add_header('Content-ID', f'<{cid}>')
        image.add_header('Content-Disposition', 'inline')
        
        # 添加到邮件
        msg.attach(image)
        print(f"已嵌入图片: {image_path} (CID: {cid})")
        return True

    def send_email(self, recipient_email, recipient_name, subject, content, attachments=None, embedded_images=None):
        """
        发送单封邮件
        :param recipient_email: 收件人邮箱
        :param recipient_name: 收件人姓名
        :param subject: 邮件主题
        :param content: 邮件内容
        :param attachments: 附件路径列表
        :param embedded_images: 嵌入图片列表 [{'cid': 'img1', 'path': 'path/to/img.jpg'}, ...]
        :return: 是否发送成功
        """
        try:
            # 创建邮件对象
            msg = MIMEMultipart()
            # 设置发件人
            msg['From'] = formataddr([self.sender_name, self.sender_email])
            # 设置收件人
            msg['To'] = formataddr([recipient_name, recipient_email])
            # 设置邮件主题
            msg['Subject'] = Header(subject, 'utf-8')
            
            # 添加嵌入图片
            if embedded_images:
                for img in embedded_images:
                    self.add_embedded_image(msg, img['cid'], img['path'])
            
            # 创建HTML内容
            html_content = self.create_html_template(recipient_name, subject, content, embedded_images)
            msg.attach(MIMEText(html_content, 'html', 'utf-8'))
            
            # 添加附件
            if attachments:
                for attachment_path in attachments:
                    self.add_attachment(msg, attachment_path)
            
            # 连接SMTP服务器并发送
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.ehlo()
                server.starttls()  # 使用TLS加密
                server.login(self.sender_email, self.sender_password)
                server.send_message(msg)
                
            print(f"成功发送邮件到 {recipient_email}")
            return True
        
        except Exception as e:
            print(f"发送邮件到 {recipient_email} 时出错: {str(e)}")
            return False

    def batch_send_from_db(self, subject, content_template, attachments=None, embedded_images=None):
        """
        从数据库批量发送邮件到未发送的邮箱
        :param subject: 邮件主题
        :param content_template: 邮件内容模板，可包含{name}占位符
        :param attachments: 附件路径列表
        :param embedded_images: 嵌入图片列表 [{'cid': 'img1', 'path': 'path/to/img.jpg'}, ...]
        :return: 成功发送和失败的邮件计数
        """
        success_count = 0
        fail_count = 0
        
        try:
            # 获取未发送的邮箱
            unsent_emails = get_unsent_emails()
            
            if not unsent_emails:
                print("没有找到未发送的邮箱")
                return success_count, fail_count
                
            print(f"找到 {len(unsent_emails)} 个未发送的邮箱")
            
            for email, tag in unsent_emails:
                # 使用标签作为收件人姓名
                name = tag if tag else email.split('@')[0]
                
                # 个性化内容替换
                personalized_content = content_template.format(name=name)
                
                # 发送邮件
                if self.send_email(email, name, subject, personalized_content, attachments, embedded_images):
                    success_count += 1
                    # 标记为已发送
                    mark_email_sent(email)
                else:
                    fail_count += 1
                
                # 添加延迟防止被服务器拒绝
                time.sleep(10)
        
        except Exception as e:
            print(f"批量发送邮件时出错: {str(e)}")
        
        print(f"\n发送完成! 成功: {success_count}, 失败: {fail_count}")
        return success_count, fail_count


def create_example_csv():
    """
    创建一个示例CSV文件，用于批量发送邮件
    """
    example_content = """姓名,邮箱
张三,<EMAIL>
李四,<EMAIL>
王五,<EMAIL>
赵六,<EMAIL>"""
    
    try:
        with open("contacts.csv", "w", encoding="utf-8") as f:
            f.write(example_content)
        print("已创建示例联系人文件: contacts.csv")
    except Exception as e:
        print(f"创建示例文件失败: {str(e)}")


def main():
    # 初始化数据库
    init_database()
    
    # 固定配置
    sender_email = "<EMAIL>"  # 发件人邮箱
    sender_password = "MFWjw7dWTsJQQLj3"    # 授权码
    
    # 邮件主题
    subject = "突破cursor限制，可无限使用claude3.7等高级模型"
    
    # 邮件内容模板 (使用{name}作为收件人姓名占位符)
    content_template = """尊敬的用户：

使用cursor出现如下提示或者任何问题，这个github项目可一键完美解决，无需复杂的操作
1. Please upgrade to pro
2. Free users can only use GPT 4.1 or Auto as premium models
3. Too many free trial accounts used on this machine
4. 或其他任何提示

项目地址：https://github.com/yunzhong-code/cursor-star
"""
    
    # CSV文件路径
    csv_file = "contacts.csv"  # 请确保此文件存在且格式正确：姓名,邮箱
    
    # 如果CSV文件不存在，则创建示例文件
    if not os.path.exists(csv_file):
        print(f"未找到联系人文件: {csv_file}")
        create_example_csv()
        print("已创建示例文件，可继续使用或修改后再使用")
    
    # 导入CSV中的邮箱到数据库
    import_emails_from_csv(csv_file)
    
    # 设置图片
    image_path = "cursor_star.jpg"  # 图片路径
    embedded_images = None
    attachments = None
    
    # 检查图片是否存在
    if os.path.exists(image_path):
        # 设置图片作为嵌入图片
        embedded_images = [{"cid": "cursor_img", "path": image_path}]
        # 同时也作为附件发送
        attachments = [image_path]
    else:
        print(f"图片文件不存在: {image_path}")
        print("请确保 cursor_star.jpg 文件位于程序同一目录下")
    
    print("系统配置:")
    print(f"- 发件人: {sender_email}")
    print(f"- 邮件主题: {subject}")
    if embedded_images:
        print(f"- 嵌入图片: {image_path}")
    if attachments:
        print(f"- 附件: {image_path}")
    
    # 创建邮件发送器
    sender = EmailSender(sender_email, sender_password)
    
    # 批量发送邮件给未发送的邮箱
    print("\n开始发送邮件...")
    sender.batch_send_from_db(subject, content_template, attachments, embedded_images)


def create_example_image():
    """
    创建一个示例图片文件，用于邮件测试
    """
    # 这里只是一个占位函数，实际应用中可以提供图片或指导用户添加图片
    print("请准备一个名为 'cursor_star.jpg' 的图片文件放在程序同目录下")
    print("您也可以修改main函数中的image_path变量来使用其他图片")


if __name__ == "__main__":
    main()


# 163邮箱：<EMAIL>
# 授权码：MFWjw7dWTsJQQLj3