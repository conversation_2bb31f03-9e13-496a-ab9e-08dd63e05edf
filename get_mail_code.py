# fetch("https://2925.com/mailv2/maildata/MailList/mails?Folder=Inbox&MailBox=mailtosun%402925.com&FilterType=0&PageIndex=1&PageCount=25&traceId=877157e216f9", {
#   "headers": {
#     "accept": "application/json, text/plain, */*",
#     "accept-language": "zh-CN,zh;q=0.9",
#     "authorization": "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IkFEMUZFNTBGNTM0MUFCMzkzRkM4RUQ3QUZBODc4ODIxNkY3MzA5MTQiLCJ0eXAiOiJhdCtqd3QiLCJ4NXQiOiJyUl9sRDFOQnF6a195TzE2LW9lSUlXOXpDUlEifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ls864PuspbTdst-_53WUZYOYPgErxTrVjSSQ3TSgk22Bv1zj8YA88MV16vrRozBXEdby4XIoQp9tsESadb0cKiRO-05W0hSjFNiD1xVLB7dyYrvmTMY3y9pe-wsZmmbYMd0yw-u4qkuueSVfHHW4s6FXvYAQCJDP2HZwYIoyhBaBCJm81yPsixDYH9a3sTHSx3TUcmZRp9wxWZXzLOH96N6fOzBT8mJlMvywE-8JsiX3vGz7A48QmXdAnxL88tYv6o7YM2Cs9lB7NSIbNIN4im7Uhl-d4Q4dw2G78g-Lxp91Bsvp7Z5Xp-4RGhHguBGm0M937XhoOGkBOvC6tR8sYg",
#     "cache-control": "no-cache",
#     "deviceuid": "5fce6dfe-5898-4ec4-a30e-9eadd7ef754f",
#     "pragma": "no-cache",
#     "priority": "u=1, i",
#     "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
#     "sec-ch-ua-mobile": "?0",
#     "sec-ch-ua-platform": "\"macOS\"",
#     "sec-fetch-dest": "empty",
#     "sec-fetch-mode": "cors",
#     "sec-fetch-site": "same-origin",
#     "x-requested-with": "XMLHttpRequest",
#     "Referer": "https://2925.com/",
#     "Referrer-Policy": "strict-origin-when-cross-origin"
#   },
#   "body": null,
#   "method": "GET"
# });

import requests
import json
import re

# 获取JWT token
def get_jwt_token(cookie_value):
    """
    获取JWT token，用于API授权
    
    参数:
    cookie_value: 您的Cookie值，用于身份验证
    
    返回:
    tuple: (更新后的cookie字符串, 新的JWT token)
    """
    url = "https://2925.com/api/auth/token?traceId=cd8bd3fd3209"
    
    # 请求头
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "cache-control": "no-cache",
        "pragma": "no-cache",
        "sec-ch-ua": "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "cookie": cookie_value,
        "Referer": "https://2925.com/",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }
    
    try:
        # 发送请求
        response = requests.get(url, headers=headers)
        
        # 检查响应状态
        if response.status_code == 200:
            try:
                # 尝试解析JSON响应
                json_resp = response.json()
                if json_resp.get("code") == 200 and json_resp.get("result"):
                    new_token = json_resp.get("result")
                else:
                    new_token = response.text.strip('\"')  # 兼容之前的格式
                
                # 更新cookie中的jwt_token
                if "jwt_token=" in cookie_value:
                    # 使用正则表达式替换jwt_token部分
                    updated_cookie = re.sub(r'jwt_token=[^;]+', f'jwt_token={new_token}', cookie_value)
                else:
                    # 如果cookie中没有jwt_token，则添加
                    updated_cookie = cookie_value + f"; jwt_token={new_token}"
                
                print("JWT token已更新")
                return updated_cookie, new_token
            except json.JSONDecodeError:
                # 如果不是JSON格式，按原来的方式处理
                new_token = response.text.strip('\"')
                
                # 更新cookie中的jwt_token
                if "jwt_token=" in cookie_value:
                    updated_cookie = re.sub(r'jwt_token=[^;]+', f'jwt_token={new_token}', cookie_value)
                else:
                    updated_cookie = cookie_value + f"; jwt_token={new_token}"
                
                print("JWT token已更新")
                return updated_cookie, new_token
        else:
            print(f"获取JWT token失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return cookie_value, None
    except Exception as e:
        print(f"获取JWT token时发生错误: {str(e)}")
        return cookie_value, None

# 第一个请求：获取邮件列表
def get_mail_list(cookie_value, jwt_token=None, retry=True, mail_box="<EMAIL>"):
    """
    获取邮件列表
    
    参数:
    cookie_value: 您的Cookie值，用于身份验证
    jwt_token: JWT token，如果为None则会自动获取
    retry: 是否在401错误时重试
    mail_box: 邮箱地址，默认为"<EMAIL>"
    
    返回:
    dict: 邮件列表数据
    """
    # 如果未提供token，则获取一个
    if jwt_token is None:
        cookie_value, jwt_token = get_jwt_token(cookie_value)
        if jwt_token is None:
            return {"code": 401, "message": "获取JWT token失败"}
    
    url = "https://2925.com/mailv2/maildata/MailList/mails"
    
    # URL参数
    params = {
        "Folder": "Inbox",
        "MailBox": mail_box,
        "FilterType": "0",
        "PageIndex": "1",
        "PageCount": "25",
        "traceId": "877157e216f9"
    }
    
    # 请求头
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "authorization": f"Bearer {jwt_token}",
        "cache-control": "no-cache",
        "deviceuid": "5fce6dfe-5898-4ec4-a30e-9eadd7ef754f",
        "pragma": "no-cache",
        "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"macOS\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "x-requested-with": "XMLHttpRequest",
        "Referer": "https://2925.com/",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "cookie": cookie_value
    }
    
    try:
        # 发送请求
        response = requests.get(url, params=params, headers=headers)
    
        # 检查响应状态
        if response.status_code == 200:
            try:
                return response.json()
            except json.JSONDecodeError:
                print("解析响应JSON失败")
                return {"code": 400, "message": response.text}
        # 如果是401错误且启用了重试，则重新获取token并重试
        elif response.status_code == 401 and retry:
            print("Token无效，正在重新获取...")
            cookie_value, new_token = get_jwt_token(cookie_value)
            if new_token:
                return get_mail_list(cookie_value, new_token, False, mail_box)  # 重试一次，防止无限循环
            else:
                print("重新获取token失败")
                return {"code": 401, "message": "重新获取token失败"}
        else:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return {"code": response.status_code, "message": response.text}
    except Exception as e:
        print(f"获取邮件列表时发生错误: {str(e)}")
        return {"code": 500, "message": str(e)}

# 第二个请求：读取邮件内容
def read_mail(message_id, cookie_value, jwt_token=None, retry=True, mail_box="<EMAIL>"):
    """
    读取特定邮件的内容
    
    参数:
    message_id: 邮件ID
    cookie_value: 您的Cookie值，用于身份验证
    jwt_token: JWT token，如果为None则会自动获取
    retry: 是否在401错误时重试
    mail_box: 邮箱地址，默认为"<EMAIL>"
    
    返回:
    dict: 邮件内容数据
    """
    # 如果未提供token，则获取一个
    if jwt_token is None:
        cookie_value, jwt_token = get_jwt_token(cookie_value)
        if jwt_token is None:
            return {"code": 401, "message": "获取JWT token失败"}
            
    url = "https://2925.com/mailv2/maildata/MailRead/mails/read"
    
    # URL参数
    params = {
        "MessageID": message_id,
        "FolderName": "Inbox",
        "MailBox": mail_box,
        "IsPre": "false",
        "traceId": "14cfa9c2d50e"
    }
    
    # 请求头
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "authorization": f"Bearer {jwt_token}",
        "cache-control": "no-cache",
        "deviceuid": "5fce6dfe-5898-4ec4-a30e-9eadd7ef754f",
        "pragma": "no-cache",
        "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"macOS\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "x-requested-with": "XMLHttpRequest",
        "Referer": "https://2925.com/",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "cookie": cookie_value
    }
    
    try:
        # 发送请求
        response = requests.get(url, params=params, headers=headers)
    
        # 检查响应状态
        if response.status_code == 200:
            try:
                return response.json()
            except json.JSONDecodeError:
                print("解析响应JSON失败")
                return {"code": 400, "message": response.text}
        # 如果是401错误且启用了重试，则重新获取token并重试
        elif response.status_code == 401 and retry:
            print("Token无效，正在重新获取...")
            cookie_value, new_token = get_jwt_token(cookie_value)
            if new_token:
                return read_mail(message_id, cookie_value, new_token, False, mail_box)  # 重试一次，防止无限循环
            else:
                print("重新获取token失败")
                return {"code": 401, "message": "重新获取token失败"}
        else:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return {"code": response.status_code, "message": response.text}
    except Exception as e:
        print(f"读取邮件内容时发生错误: {str(e)}")
        return {"code": 500, "message": str(e)}


# 在get_mail2.py中添加此函数

# 在get_mail2.py中添加这个函数，放在if __name__ == "__main__"之前

def get_2_code(target_email, target_subject="Sign up for Cursor"):
    """
    获取指定邮箱的验证码
    
    参数:
    target_email: 目标邮箱地址
    target_subject: 邮件主题，默认为"Sign up for Cursor"
    
    返回:
    str/None: 成功时返回验证码字符串，失败时返回None
    """
    # 请在此处填写您的cookie值
    your_cookie = "returnURL=%2Fconnect%2Fauthorize%2Fcallback%3Fclient_id%3DB9257F7F9B1EF15CE%26redirect_uri%3Dhttps%253A%252F%252F2925.com%252Fauth%252Fsignin-oidc%26response_type%3Dcode%2520id_token%26scope%3Dopenid%2520profile%25202980_client%40web%2520offline_access%26response_mode%3Dform_post%26nonce%3D638834983980400207.NzIzMDRlMDEtM2FlNi00NTY0LWEzNDYtYTdmMDQ5NTg3M2I0M2YyYmI2MGItYWY4Mi00ZGJjLWFjMzQtMjAwNTkxOWFmMTQx%26state%3DCfDJ8LvNtK7K79pCgl25c3WvKz2RElUohqu9VnlUEZPbjlLoK0qUVkD24wOF-jdkoS8oBvWvL42U07m1hJsEP6TfYRCUIyswcydNL_MUJ6UXx2KsO64PX_XgDmwzHgvNCUO0gDuC9EHQRgljpflop9Zto4FByveiUrgs85AK0ok0G6cG4cxSQj1cMC7H9C0XJ6f1iEKImKAgnZPuUk5qMMsCFYyTRS9A-5851E_dFl16VBVY7RzAMki9aPhyFSgQGsRUMwEGrDVjA1j5amC0izHkQ5F8pEQ_5ystJFWqSSf0b1Zl8hsWtVmSRI7d1DLgmSoVF1IaWc8cm8pSC3ca0-HVDtyraIumxdGcjvdozWBjNnvdUihCMJxvkg31In_owk6f3Wz2bP3jlLkNb-crUDOZ4hE%26x-client-SKU%3DID_NETSTANDARD2_0%26x-client-ver%3D5.5.0.0; LoginType=1; .AspNetCore.Cookies=chunks-2; .AspNetCore.CookiesC1=CfDJ8LvNtK7K79pCgl25c3WvKz3t2poE6Tp5Dn-gXtZPHSRRjORpBRY89htmkIRwk5YQJk2YLVyL5V8VlGrpDcGMnNTq2K-o2KEWa5cqPc8diWE8L70DBffuv8Mb9apWYFdLx7YvHX2b83FB9DPThYXzgmDzPinBasiEGGx1Xv0EJnTAHaYe6iMTzytn9h_mkb_CbcBujcmRQmCsQDulmrp1Dq51aYeozAGudRILUSoYYTTk-tzPWJrZuINgrHEFv3ixwsVNqbcxnhcRFupoN2TPJ6Nmll7bHhrKkHLr-C12ZHhOnFnIgAzgALtE0hcKSn5-ALvY-fConOUaKr4v2FnwgcaevRw3NB5KkosN8yN6nIc3d8P6zqrV7QT3uxWx1FeXpAISMVWnJCb5VCO5ML9D8wNZvtU54S-ThRJYkBf0hlMYMD9mUQg-yZuJJlAQ_admO64enFmkNyu9MtR-L2aLQ9T6ns8CPi3qjIJReLlhYiroMoYdSQYtquFffAlTKucNRWQzlOXqy7xVhBFHeYWTSe66c43iO-0e8-y1ChOU_1zv8nwqlvhn-t9Iog5HR85z43SH7vULScFiCuwpu99RkgipIlsDR26OuJx8b7VBJv-wtKMkvOJ11pDjQ0s8M_4HN8yd8Hv_lGlKtpuOsL_hruWDgFsy8zyz9OwaFI_dBqsHeHDOYTx5lRkEUKpUIjZ026yDBb8gtNivOuOz45euzPaffQvTQ5jrpDtA826_3yrFf6B4Qjym-H2H414nyb2RRfAxbJhrTZhCNO9Bsw74wiihrzgyiBBr25i_S5r4fCm31HIaBe4ASCO6nEGngcUUUd45cykK1ax10qewFcJONiB4jCk5nUrYOESgHBXDKmsqesqw6A_4gHJ3iQdnqhxq0wsQOJqtAReqgE1zz96a0r36M59wNDQnIZ86Q85TxmN-BNunbq0rkWPc1v9dK9iKiEL6hrgPnoaIXOhqNE_72Sa-stVW0pZo2MtY9HV1PmwZzr5MbzytlsVtD0vxLnMdPlgNqkNGPsc6Da8sDfSlTSXh5p7As4hB57ZSSf5EPRkbdjMnPfUB3-9HTsnrIKXmifAaJouUjwXnc4RD4a-Kiw_BOuObgWpyuKcb3zLnUT4oI7a3MO2prfihfZ3sjKdRQa-kl12PJLdr9vexgPso7PCNIxzLrR65nssigA37VXHwjO_Z09GrwcRm1mBjhep9GbJhMWKFY-FQquYxN1tS48c-xU5WMcMbkO__DXx1zjTEQDNg_k8ltSDACALn168hBkhEHBgX7a3QpxZ1MI5pqG2F5TlY4zvmqQbj1ffS9N4J7ndnNP6yWrBmvy_qMpkyL15i599Wb2-EOIO_1RJI6krTmmQEXc2ltJ4pP6lTuxxBist87zgmC_6rqBNhmNiIKPnxT6cNob0MiYVta9sGhHzUBpbGHxYcUYwDSDws4jDe1HtmB5VheQvxH67ZH8Om3TeL4RGn_rwb6aqX5k_vNA7kGjlsrdxPaUJ6LkRpn3Wc-7Pm8-AXOvTjt6lNGyxAQlWju-_uul_65h_NKmp5OAwq76dXRWOthp4cFMhvTuwYq4R0REWPzXIn2rQn821yqun-WFAd9rWCv1bqQXJllEy1Duso9Cfi2dtgbW2LcT0zE54DU9KF-2qWB08PzySn3emBdeslM6aVV3XhQ7e4BcNTyJkk7KikBsEJxS_CsIZ-kKLSJgFMDhgBklD1OSJyuvaN9RwdgANAIorm0fKOBdRBZeF8-hBoCg0_yU8KIsN18odDevUvn6LbQFupRK-CVpdmVJYYbyLrd6xe05hnFL5LyDvM2el2dZh6tGhMT8QJYlwEDhn9zDOxGsnvbKORKqYltEKpiTZ0URiNUR4E6AVXzhjnvEsXXNFnHJ62RkYntM93yEqWq2VpU_Ls-25YLWePvXjNt86TcwuXRBQemwJ8p9-clSwnhxGmIHXpwdT29UFdsdwgzhx5mCJ5f3r7_BbaMkQFI-OGeY93ZPANGz80wnWegoTk0w132YCHqpxisAIX-m5aj5MfH0bzwedUXhgVRPEUb-XBYtnil_z-Ly5hy8_LDTGqkMGc8MfxGnj8rXJBvckx5Xp3br9KeyyZPYXuvDME_NX10r_J0ASE69uzG0GKySoyfgj400CGxacXUyzphEzRzB1pMqvAp5uOwIONIrqGAyDh6PMT4E3_Hb6eBhgIBiKlWw3RP8Zbd39VHQLYRUth3OULCYFEK8yax2LgixV2fqA-zCBhjse4FvFdQRihHWWqFXuJ72bvKvRYygmuwZPHETsHziWOz_ct9804XrVC3wNSg_NvpoW0L52cPXQQCdeFQeLvSxYgRQtx9GfXWs7wxFC5L0I7VU-5kYL7z0jvfOrxwSQQY68aTv_e_TCU9x3ZJANTXvUChyYBngp09J0UIR3CVJVx_zED1cozBp9cK7GZBPlsCU95rPuclN0UpdCMloyv7JIyzvD6psDhfuCp6L89yl4-7Ibp-pxJKm0UxInQVj-cwMb-VUR9e14-pGQ0EDEY9apqjpA9seOdM6V_5R6XP-rt4G948YkHavvns_dzyYT3MBo2dbSW_slXNtNIdnGZXGAsDv2otypCtjbfpLbUnAJAF3rmdETesnHfu3nG40oueZhJCL8iWovBiLJzgnP4lBgQAgmB6jAVbfJyIz0cXjKAG3ajpd2kDHVkg_r5YoKNs3L54YMbm-CBagHbab6deJbBpS83mJCuiQSJwVaFKTwy2ag4LVeSP2AQyDZ0fCa2sBfL5jJjoG151YG1pWkVwEVA2boMGm8f0A-e0259bpaOt1qqWrQi-9soEoHbArFUvslcHumo7a3druYREWFPCYZs3_5xIKlRdgHAtMvQSgz670DIaq562vfRd9xjk6luYbTarJ9lGW5U5X2DPT9i8b8YIsf9ZgVAw4VLDLrx6ByvnXrloBYKLsEizdmjV0wIrfvNclfXkpw3IBz0AmjSQVZz4mlRP6YWvC641ZqZWixZTu8J4NYexG7lv0fLa5tTAOUR5fW55AkPpOX3w3Cs5ymocz_KmRAIzg9kkKUs7Kz1Ny_ehSQ5uPmrnyDErvS6KqJBUzYaKpLJIMCILlw6GkeaQRy39vbKXbJwaEzWyxtAvGxUEZmJ5kN34lfJGzAD74MjtR46nUKwkwsFIIyVjg2R9OmUNFBlFNbkRJMAvtWMY94OIYAgTWBDFPBA72Cq4OPYm_1m40kawb4GjGrAWd4J9XCSIOB8dN5i2Q5YGVhBQiJgktknednwa7xHAHY0IW1QBzEVq2ESzG5EuZapWhay4a0CQ3yrXnbuURNAVDv3dAwY1y8cwpq-rIZTIpNPrI05KV6T_tF37Fy6DWqMq0pLOkZARhLjEDU7WMKTqCuzH0a6uzEGTLWHUxWq_fGkx3G4BeZEvLYNF8GJAuf-T_tcbIbDE-5pBUgD__nFqSut5Cb_T1ZtT9-b6aWCXkH7KLCPLH-1D4W9js2X1Dq01G75KtpUvQ7KphMcrkuMpkBwXrB0W_2USii_O-pg9s2MFOsms2l-7i09fKLsauDTLe5yNB9ghBn0FIJoGFoIzDEbtUU2e2o0ZOxWdSVroMg8ZMyrYe7J6cTaqcnvtyaVyVMo5tRDdRFuNJHcf7QDJkf7AfmlEve0KwCSvM1JvRiIgZxADA9pKOw833Y48iPEsLaPRqmq6INmoU5HTwBJ8WYY0I75tKVri5LoXMgRjT0MOqdnHgqasj_3_67VzIMdVtFfjLCXMlhvFoI3vHsqKdMX4UMA9hs1DOoSPlDC7tF1ikAUz9QruWqmIVyCZmMaoD4ZOdqFQI_0Xxl5N2jWrHuH1Rvm77wXn31ojG-Wlfj53gmHk9xbE7Y-JeslawqzRXmadOno9GGyE_KuDHsEBf0IUhuI2aXJHTeOH5ShhReP9wDU5W0UIWNPgkKSMtPirxXF50HMgEixcbLvbcgWYuv_e562qhJ6L0NTfC920LPUX4rbA11qnTRBi; .AspNetCore.CookiesC2=gO1FjI8woTsZe_-eSPrAGyTtxGj32OpAxD4CKo2IxSLHRkxU8LNQx3atq6bZf02YoEDUWMcH37vbehrQv4fI7xCzqWuvGKsUC7sdatZZcmOz-7eeRnmXsdk_wqarG0yObeZtjLj5g25q501hgTxGPsyRbdgnSy148eDq-77bTuf8YdOGXKfCUOQCMSpoaywJzvKANly7kJZVDSUXHOz2iEO5xFyIOyPuhlJPeSjdmjCTKg4SUwLNJNALeqw4-A8IP10mXosp31K9893fBdvTHE9krZ6DpTJx1ZRonAcPF6Sg3hl701UWdqrWI3tjJkqBW7_elQpwj49UX3-OAumv1eaI2q0vtM-NGhMRDPi41Zknf8LJaUS29lwFxrPXe9oNJYk-XiXwIrKpFXICk5f2POcF3x7anA0GeH-olR9s5c8xDiWFFEdguFzB84Jst2HI9_YsUjTGkiDU1y1ZYF7Ioy-65qQMyUpv1N6H6UDDlsUepJFZJaHV_2yf-2cEvysDbCMJakMLRwbvBagh5GwPrzqndDDULUSjb3tXsCoJVmYbOpSx4ultbVzXrQ"  # 用户自行填写cookie值
    
    if not your_cookie:
        print("请在脚本中填写cookie值")
        return None
    
    # 获取JWT token并更新cookie
    your_cookie, jwt_token = get_jwt_token(your_cookie)
    if not jwt_token:
        print("JWT token获取失败，请检查cookie是否有效")
        return None
        
    # 获取邮件列表
    mail_list_response = get_mail_list(your_cookie, jwt_token, True, target_email)
    
    if not (mail_list_response and mail_list_response.get("code") == 200):
        print(f"获取邮件列表失败。响应: {mail_list_response}")
        return None
    
    # 查找匹配的邮件
    found_mail = None
    if mail_list_response.get("result") and mail_list_response["result"].get("list"):
        for mail_item in mail_list_response["result"]["list"]:
            # 检查收件人地址和主题是否匹配
            if target_email in mail_item.get("toAddress", []) :
                found_mail = mail_item
                break
    
    if not found_mail:
        print(f"未在邮件列表中找到发给 '{target_email}'' 的邮件。")
        return None
    
    # 读取邮件内容
    message_id = found_mail.get("messageId")
    print(f"找到匹配邮件: MessageID = {message_id}")
    
    mail_content_response = read_mail(message_id, your_cookie, jwt_token, True, target_email)
    if not (mail_content_response and mail_content_response.get("code") == 200):
        print(f"读取邮件内容失败。响应: {mail_content_response}")
        return None
    
    print("成功读取邮件内容")
    
    # 提取验证码
    body_text = mail_content_response.get("result", {}).get("bodyText")
    if not body_text:
        print("邮件内容中未找到bodyText。")
        return None
    
    # 提取验证码 - 方法1: 查找全部由数字组成的行，通常验证码是以数字形式出现的
    verification_code = None
    
    # 方法1: 查找带空格的数字行 (如 "1 3 0 8 5 9")
    code_lines = [line.strip() for line in body_text.splitlines() 
                 if line.strip().replace(" ", "").isdigit() 
                 and len(line.strip().replace(" ", "")) > 3]
    
    if code_lines:
        # 尝试从包含多个数字的行中提取，例如 "1 3 0 8 5 9"
        for line in code_lines:
            if " " in line and len(line.split()) > 3:  # 检查是否有空格且数字个数大于3
                verification_code = line
                print(f"通过方法1找到验证码: {verification_code}")
                break
    
    # 方法2: 如果方法1未找到验证码，尝试匹配"Verify your email"格式中的数字
    if not verification_code:
        # 查找纯数字行，通常在"This code expires"之前的位置
        for line in body_text.splitlines():
            line = line.strip()
            if line.isdigit() and len(line) >= 6:  # 验证码通常是6位或更长
                verification_code = line
                print(f"通过方法2找到验证码: {verification_code}")
                break
        
        # 如果还是没找到，尝试使用正则表达式匹配特定模式
        if not verification_code:
            # 匹配"Enter the code below"之后的纯数字
            enter_code_pattern = re.search(r'Enter the code below.*?\n\s*(\d{6,})', body_text, re.DOTALL)
            if enter_code_pattern:
                verification_code = enter_code_pattern.group(1)
                print(f"通过正则表达式匹配'Enter the code below'后的数字找到验证码: {verification_code}")
            else:
                # 匹配两个空行之间的纯数字
                between_lines_pattern = re.search(r'\n\s*\n\s*(\d{6,})\s*\n\s*\n', body_text)
                if between_lines_pattern:
                    verification_code = between_lines_pattern.group(1)
                    print(f"通过正则表达式匹配空行之间的数字找到验证码: {verification_code}")
    
    if verification_code:
        print(f"提取到的验证码: {verification_code}")
        return verification_code
    else:
        print(f"在邮件正文中未能按预期格式找到验证码。邮件正文: \n{body_text}")
        return None




# 使用示例
if __name__ == "__main__":

    print("哈哈")

    email = get_2_code("<EMAIL>")
    # 请在此处填写您的cookie值
    # your_cookie = ""  # 用户自行填写cookie值
    
    # # 要搜索的目标邮箱和邮件主题
    # target_email = "<EMAIL>"  # 指定要查找的目标邮箱
    # target_subject = "Sign up for Cursor"  # 指定要查找的邮件主题
    
    # if not your_cookie:
    #     print("请在脚本中填写您的 cookie 值 (your_cookie变量)。")
    # else:
    #     # 获取JWT token并更新cookie
    #     your_cookie, jwt_token = get_jwt_token(your_cookie)
    #     if jwt_token:
    #         print("JWT token获取成功")
            
    #         # 获取邮件列表
    #         mail_list_response = get_mail_list(your_cookie, jwt_token, True, target_email)
            
    #         if mail_list_response and mail_list_response.get("code") == 200:
    #             print("成功获取邮件列表")
    #             # print(json.dumps(mail_list_response, ensure_ascii=False, indent=2)) # 可选：打印完整的邮件列表响应
                
    #             found_mail = None
    #             if mail_list_response.get("result") and mail_list_response["result"].get("list"):
    #                 for mail_item in mail_list_response["result"]["list"]:
    #                     # 检查收件人地址和主题是否匹配
    #                     if target_email in mail_item.get("toAddress", []):
    #                         found_mail = mail_item
    #                         break
                
    #             if found_mail:
    #                 message_id = found_mail.get("messageId")
    #                 print(f"找到匹配邮件: MessageID = {message_id}")
                    
    #                 # 读取邮件内容
    #                 mail_content_response = read_mail(message_id, your_cookie, jwt_token, True, target_email)
    #                 if mail_content_response and mail_content_response.get("code") == 200:
    #                     print("成功读取邮件内容")
    #                     # print(json.dumps(mail_content_response, ensure_ascii=False, indent=2)) # 可选：打印完整的邮件内容响应
                        
    #                     body_text = mail_content_response.get("result", {}).get("bodyText")
    #                     if body_text:
    #                         # 提取验证码 - 查找全部由数字组成的行，通常验证码是以数字形式出现的
    #                         code_lines = [line.strip() for line in body_text.splitlines() if line.strip().replace(" ", "").isdigit() and len(line.strip().replace(" ", "")) > 3]
                            
    #                         verification_code = None
    #                         if code_lines:
    #                             # 尝试从包含多个数字的行中提取，例如 "1 3 0 8 5 9"
    #                             for line in code_lines:
    #                                 if " " in line and len(line.split()) > 3:  # 检查是否有空格且数字个数大于3
    #                                     verification_code = line
    #                                     break
                            
    #                         if verification_code:
    #                             print(f"提取到的验证码: {verification_code}")
    #                         else:
    #                             print(f"在邮件正文中未能按预期格式找到验证码。邮件正文: \n{body_text}")
    #                     else:
    #                         print("邮件内容中未找到 bodyText。")
    #                 else:
    #                     print(f"读取邮件内容失败。响应: {mail_content_response}")
    #             else:
    #                 print(f"未在邮件列表中找到发给 '{target_email}'且主题为 '{target_subject}' 的邮件。")
    #         else:
    #             print(f"获取邮件列表失败。响应: {mail_list_response}")
    #     else:
    #         print("JWT token获取失败，请检查cookie是否有效")

