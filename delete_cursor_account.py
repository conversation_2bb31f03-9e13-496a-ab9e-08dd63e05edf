# 参考 cursor_automation.py ，使用pyautogui,坐标点每个电脑不一样，需要维护,
# 数据库文件 cursor_auth_store.db   表cursor_auth,
        # CREATE TABLE IF NOT EXISTS cursor_auth (
        #     id INTEGER PRIMARY KEY AUTOINCREMENT,
        #     email TEXT,
        #     is_upload INTEGER,
        #     access_token TEXT,
        #     refresh_token TEXT,
        #     password TEXT,
        #     created_at TEXT,
        #     updated_at TEXT,
        #     machine_id TEXT,                    /* machineId */
        #     telemetry_machine_id TEXT,          /* telemetry.machineId */
        #     env_machine_id TEXT,                /* environment.machineId */
        #     anonymized_id TEXT,                 /* anonymizedId */
        #     session_id TEXT,                    /* sessionId */
        #     agent_id TEXT,                      /* agentId */
        #     telemetry_anon_id TEXT,             /* telemetry.anon_id */
        #     telemetry_machine_id_hash TEXT,     /* telemetry.machineId_hash */
        #     telemetry_session_id TEXT,          /* telemetry.session_id */
        #     mac_machine_id TEXT,                /* telemetry.macMachineId */
        #     installation_id TEXT,               /* telemetry.installation_id */
        #     storage_service_machine_id TEXT,    /* storage.serviceMachineId */
        #     telemetry_sqm_id TEXT,              /* telemetry.sqmId */
        #     telemetry_dev_device_id TEXT        /* telemetry.devDeviceId */
        # )

# 获取一条数据，里面包含邮箱和密码
# 用浏览器打开一个网址，输入邮箱和密码，点击登录
# 登录成功后，跳转到指定网址
# 点击一个坐标点（Advanced），再点击一个坐标点（Delete Account）
# 再点击一个坐标点Delete Account输入框,
# 输入 delete 
# 再点击一个 delete 按钮
# 获取当前页面  是否为 https://www.cursor.com/cn
# 如果为 https://www.cursor.com/cn 则删除成功，否则删除失败
# 如果删除成功，则向一张数据表中，插入这条数据，表名是 cursor_account_delete_record
# 表字段。email。delete_count。singup_count
# 首次插入到该表时，delete_count 为 1，singup_count 为 1

import sqlite3
import requests
import json
import time
from datetime import datetime, timedelta
import logging
import webbrowser
import pyautogui
import sys
import os
import re
import pyperclip
from smart_email_verification import get_verification_code_smart

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 根据操作系统设置快捷键modifier
MODIFIER = 'command' if sys.platform == 'darwin' else 'ctrl'

#######################################################
# PC配置管理 - 不同电脑的配置统一管理
#######################################################

# PC配置类，用于存储每台电脑的坐标和路径设置
class PCConfig:
    def __init__(self, name, description=""):
        self.name = name
        self.description = description
        # 坐标配置
        self.coords = {}
        # 路径配置
        self.paths = {}
    
    def set_coords(self, coords_dict):
        """设置坐标配置"""
        self.coords.update(coords_dict)
        return self
    
    def set_paths(self, paths_dict):
        """设置路径配置"""
        self.paths.update(paths_dict)
        return self
    
    def get_coord(self, name, default=None):
        """获取坐标配置"""
        return self.coords.get(name, default)
    
    def get_path(self, name, default=None):
        """获取路径配置"""
        return self.paths.get(name, default)

# 创建不同电脑的配置
pc_configs = {}

# PC1 配置
pc1 = PCConfig("pc1", "MacBook Pro 13")
pc1.set_coords({
    "LOGIN_EMAIL": (630, 445),        # 登录邮箱输入框
    "CONTINUE_BUTTON": (651, 498),    # continue按钮
    "LOGIN_PASSWORD": (625, 514),     # 密码输入框
    "LOGIN_BUTTON": (631, 574),       # 登录按钮
    "CAPTCHA_CHECKBOX": (636, 582),   # 人机验证勾选框位置
    "SETTINGS_BUTTON": (165, 535),    # Settings按钮
    "ADVANCED_BUTTON": (535, 663),    # Advanced按钮
    "DELETE_ACCOUNT": (521, 704),     # Delete Account按钮
    "DELETE_CONFIRM_INPUT": (578, 598), # 确认删除输入框
    "DELETE_BUTTON": (943, 662),      # 最终删除按钮
    "VERIFICATION_CODE_INPUT": (605, 591),  # 验证码输入框位置
})
pc1.set_paths({
    "AUTH_DB_PATH": "cursor_auth_store.db",
})

# PC2 配置
pc2 = PCConfig("pc2", "台式机Windows")
pc2.set_coords({
    "LOGIN_EMAIL": (1674, 924),        # 登录邮箱输入框
    "CONTINUE_BUTTON": (1695, 1035),    # continue按钮
    "LOGIN_PASSWORD": (1689, 1045),     # 密码输入框
    "LOGIN_BUTTON": (1728, 1171),       # 登录按钮
    "CAPTCHA_CHECKBOX": (1709, 1158),   # 人机验证勾选框位置
    "SETTINGS_BUTTON": (885, 867),    # Settings按钮
    "ADVANCED_BUTTON": (1616, 1088),    # Advanced按钮
    "DELETE_ACCOUNT": (1501, 1169),     # Delete Account按钮
    "DELETE_CONFIRM_INPUT": (1597, 1202), # 确认删除输入框
    "DELETE_BUTTON": (2270, 1310),      # 最终删除按钮
    "VERIFICATION_CODE_INPUT": (1661, 1168),  # 验证码输入框位置（Windows）
})
pc2.set_paths({
    "AUTH_DB_PATH": "cursor_auth_store.db",
})

# 注册PC配置
pc_configs["pc1"] = pc1
pc_configs["pc2"] = pc2

# 设置当前使用的PC配置
# 如果需要切换PC，只需修改这一行
current_pc_name = "pc2"  # 修改为需要使用的PC配置名称
current_pc = pc_configs[current_pc_name]

# 打印当前使用的配置
print(f"使用PC配置: {current_pc.name} - {current_pc.description}")

def connect_db(db_path=None):
    """连接到SQLite数据库"""
    if db_path is None:
        db_path = current_pc.get_path("AUTH_DB_PATH")
        
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        return conn
    except sqlite3.Error as e:
        logger.error(f"数据库连接失败: {e}")
        raise

def get_account_to_delete():
    """从数据库获取一个要删除的账号，按ID从小到大排序"""
    conn = connect_db()
    try:
        cursor = conn.cursor()
        
        # 直接从cursor_auth表获取记录，不联接任何其他表
        cursor.execute("""
            SELECT email, password 
            FROM cursor_auth
            ORDER BY id ASC
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            return {"email": result['email'], "password": result['password']}
        else:
            logger.info("没有找到需要删除的账号")
            return None
    except sqlite3.Error as e:
        logger.error(f"查询账号失败: {e}")
        return None
    finally:
        conn.close()

def create_delete_record_table():
    """创建删除记录表(如果不存在)"""
    conn = connect_db()
    try:
        cursor = conn.cursor()
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS cursor_account_delete_record (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE,
            delete_count INTEGER DEFAULT 0,
            signup_count INTEGER DEFAULT 1,
            last_delete_time TEXT,
            last_signup_time TEXT
        )
        """)
        conn.commit()
        logger.info("删除记录表创建/确认成功")
    except sqlite3.Error as e:
        logger.error(f"创建表失败: {e}")
    finally:
        conn.close()

def update_delete_record(email):
    """更新或创建删除记录"""
    conn = connect_db()
    try:
        cursor = conn.cursor()
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 检查是否已存在记录
        cursor.execute("SELECT * FROM cursor_account_delete_record WHERE email = ?", (email,))
        record = cursor.fetchone()
        
        if record:
            # 更新现有记录
            delete_count = record['delete_count'] + 1
            signup_count = record['signup_count']
            cursor.execute("""
                UPDATE cursor_account_delete_record 
                SET delete_count = ?, last_delete_time = ?
                WHERE email = ?
            """, (delete_count, now, email))
            logger.info(f"更新记录: {email}, 删除次数: {delete_count}, 注册次数: {signup_count}")
        else:
            # 创建新记录
            cursor.execute("""
                INSERT INTO cursor_account_delete_record 
                (email, delete_count, signup_count, last_delete_time, last_signup_time)
                VALUES (?, 1, 1, ?, ?)
            """, (email, now, now))
            logger.info(f"创建记录: {email}, 删除次数: 1, 注册次数: 1")
        
        conn.commit()
        return True
    except sqlite3.Error as e:
        logger.error(f"更新删除记录失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def save_initial_delete_record(email):
    """保存初始删除记录，delete_count设置为0"""
    conn = connect_db()
    try:
        cursor = conn.cursor()
        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 检查是否已存在记录
        cursor.execute("SELECT * FROM cursor_account_delete_record WHERE email = ?", (email,))
        record = cursor.fetchone()
        
        if record:
            # 如果记录已存在，不做任何操作
            logger.info(f"邮箱 {email} 的删除记录已存在，跳过初始化")
            return True
        else:
            # 创建新记录，delete_count设置为0
            cursor.execute("""
                INSERT INTO cursor_account_delete_record 
                (email, delete_count, signup_count, last_signup_time)
                VALUES (?, 0, 1, ?)
            """, (email, now))
            logger.info(f"创建初始删除记录: {email}, 删除次数: 0, 注册次数: 1")
        
        conn.commit()
        return True
    except sqlite3.Error as e:
        logger.error(f"保存初始删除记录失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def maximize_browser_window():
    """最大化浏览器窗口"""
    logger.info("最大化浏览器窗口...")
    if sys.platform == 'darwin':  # macOS
        # 使用Command+Control+F切换全屏
        pyautogui.hotkey('command', 'ctrl', 'f')
    else:
        # Windows - 使用Windows+Up快捷键进行最大化
        pyautogui.hotkey('win', 'up')

def get_current_url():
    """获取当前浏览器URL"""
    # 保存原始剪贴板
    original_clipboard = pyperclip.paste()
    
    # 复制当前URL
    pyautogui.hotkey(MODIFIER, 'l')  # 选中地址栏
    time.sleep(0.5)
    pyautogui.hotkey(MODIFIER, 'c')  # 复制地址
    time.sleep(0.5)
    
    # 获取URL
    current_url = pyperclip.paste()
    
    # 恢复剪贴板
    pyperclip.copy(original_clipboard)
    
    logger.info(f"当前URL: {current_url}")
    return current_url

def close_browser():
    """关闭浏览器"""
    logger.info("关闭浏览器...")
    try:
        if sys.platform == 'darwin':  # macOS
            # macOS方法
            browser_processes = ["Google Chrome", "Chrome", "chromedriver"]
            for process in browser_processes:
                result = os.system(f"pkill {process}")
                if result == 0:
                    logger.info(f"成功关闭{process}")
                    time.sleep(1)
                    return True
            result = os.system("killall 'Google Chrome'")
            if result == 0:
                logger.info("成功关闭Google Chrome")
                time.sleep(1)
                return True
            logger.info("在macOS上未找到或未能关闭浏览器进程")
            return False
        else:  # Windows
            logger.info("尝试在Windows上关闭浏览器...")
            exe_target = "chrome.exe"
            
            try:
                # 使用taskkill强制关闭Chrome进程
                result = os.system(f"taskkill /f /im {exe_target}")
                if result == 0:
                    logger.info(f"成功关闭{exe_target}")
                    time.sleep(2)
                    return True
                else:
                    logger.info(f"未找到{exe_target}进程或关闭失败")
                    return False
            except Exception as e:
                logger.error(f"关闭浏览器时出错: {str(e)}")
                return False
    except Exception as e:
        logger.error(f"关闭浏览器时出现主错误: {str(e)}")
        return False

def get_mouse_position():
    """获取当前鼠标位置，用于调试"""
    for i in range(5):
        current_pos = pyautogui.position()
        logger.info(f"当前鼠标位置: X={current_pos.x}, Y={current_pos.y}")
        time.sleep(1)
    return current_pos

def delete_cursor_account(email, password):
    """删除Cursor账户的自动化流程"""
    login_url = "https://authenticator.cursor.sh"
    
    try:
        # 打印当前鼠标位置用于调试
        logger.info("打印当前鼠标位置供调试...")
        position = pyautogui.position()
        logger.info(f"当前鼠标位置: X={position.x}, Y={position.y}")
        
        # 打开登录页面
        logger.info(f"正在打开网站: {login_url}")
        webbrowser.open(login_url)
        time.sleep(7)  # 等待页面加载
            # 如果是Windows系统，按一次Shift键切换输入法到英文
        if sys.platform != 'darwin': # sys.platform == 'win32' for Windows
            print("在Windows上按Shift键切换到英文输入法...")
            pyautogui.press('shift')
            time.sleep(0.5) # 短暂等待确保切换生效
        
        # 确保浏览器窗口获得焦点 - 点击页面中间位置多次
        screen_width, screen_height = pyautogui.size()
        logger.info(f"点击屏幕位置 X={screen_width // 3}, Y={screen_height // 2} 确保浏览器获得焦点")
        for i in range(3):
            pyautogui.moveTo(screen_width // 3, screen_height // 2, duration=0.5)
            pyautogui.click()
            time.sleep(0.8)
        
        # Windows系统按shift键切换输入法到英文
        if sys.platform != 'darwin':
            logger.info("在Windows上按Shift键切换到英文输入法")
            pyautogui.press('shift')
            time.sleep(0.5)
            
        # 最大化浏览器窗口
        logger.info("正在最大化浏览器窗口...")
        maximize_browser_window()
        time.sleep(3)  # 等待最大化完成
        
        # 使用当前PC配置的坐标
        login_email_x, login_email_y = current_pc.get_coord("LOGIN_EMAIL")
        login_pass_x, login_pass_y = current_pc.get_coord("LOGIN_PASSWORD")
        login_btn_x, login_btn_y = current_pc.get_coord("LOGIN_BUTTON")
        advanced_btn_x, advanced_btn_y = current_pc.get_coord("ADVANCED_BUTTON")
        delete_account_x, delete_account_y = current_pc.get_coord("DELETE_ACCOUNT")
        delete_confirm_x, delete_confirm_y = current_pc.get_coord("DELETE_CONFIRM_INPUT")
        delete_btn_x, delete_btn_y = current_pc.get_coord("DELETE_BUTTON")
        settings_btn_x, settings_btn_y = current_pc.get_coord("SETTINGS_BUTTON")
        
        # 打印一下当前坐标，用于调试
        logger.info(f"邮箱输入框坐标: X={login_email_x}, Y={login_email_y}")
        
        # 获取鼠标实际位置
        get_mouse_position()
        
        # 点击并输入邮箱 - 使用较长的duration
        logger.info(f"移动鼠标到邮箱输入框: X={login_email_x}, Y={login_email_y}")
        pyautogui.moveTo(login_email_x, login_email_y, duration=1.0)
        time.sleep(0.5)
        pyautogui.click()
        time.sleep(0.5)

        
        # 输入邮箱
        pyautogui.write(email, interval=0.1)
        logger.info(f"输入邮箱: {email}")
        time.sleep(1)
        
        # 点击continue按钮, 进入下一页
        continue_btn_x, continue_btn_y = current_pc.get_coord("CONTINUE_BUTTON")
        logger.info(f"移动鼠标到Continue按钮: X={continue_btn_x}, Y={continue_btn_y}")
        pyautogui.moveTo(continue_btn_x, continue_btn_y, duration=1.0)
        time.sleep(0.5)
        pyautogui.click()
        logger.info("点击Continue按钮")

        time.sleep(5)  # 增加等待时间，确保页面加载
        
        # 获取当前URL检查是否正确进入下一页
        current_url = get_current_url()
        logger.info(f"点击Continue后当前URL: {current_url}")

        # 密码输入页面 https://authenticator.cursor.sh/password?email=
        # 检查是否需要人机验证
        if "password" in current_url and not "www.cursor.com" in current_url:
            captcha_x, captcha_y = current_pc.get_coord("CAPTCHA_CHECKBOX")
            logger.info(f"检测到可能需要人机验证，点击验证框: X={captcha_x}, Y={captcha_y}")
            pyautogui.moveTo(captcha_x, captcha_y, duration=0.5)
            pyautogui.click()
            time.sleep(3)
            
        # 点击并输入密码
        logger.info(f"移动鼠标到密码输入框: X={login_pass_x}, Y={login_pass_y}")
        pyautogui.moveTo(login_pass_x, login_pass_y, duration=1.0)
        time.sleep(0.5)
        pyautogui.click()
        time.sleep(0.5)
        

        
        pyautogui.write(password, interval=0.1)
        logger.info(f"输入密码: {'*' * len(password)}")
        time.sleep(1)
        
        # 点击登录按钮
        logger.info(f"移动鼠标到登录按钮: X={login_btn_x}, Y={login_btn_y}")
        pyautogui.moveTo(login_btn_x, login_btn_y, duration=1.0)
        time.sleep(0.5)
        pyautogui.click()
        
        time.sleep(10)  # 等待登录成功并加载

        # 获取当前URL检查是否登录成功
        current_url = get_current_url()
        
        # 检查是否需要人机验证
        captcha_attempts = 0
        max_captcha_attempts = 3
        
        # 如果URL未变为目标URL，可能需要处理人机验证
        while captcha_attempts < max_captcha_attempts and "authenticator.cursor.sh/password" in current_url:
            logger.info("检测到可能需要人机验证，尝试点击验证框")
            
            # 获取人机验证框坐标
            captcha_x, captcha_y = current_pc.get_coord("CAPTCHA_CHECKBOX")
            
            # 点击人机验证框（使用更平滑的移动）
            logger.info(f"移动鼠标到验证框: X={captcha_x}, Y={captcha_y}")
            pyautogui.moveTo(captcha_x, captcha_y, duration=1.0)
            time.sleep(0.5)
            pyautogui.click()
            logger.info(f"已点击人机验证框，第{captcha_attempts + 1}次尝试")
            
            # 等待验证处理
            time.sleep(5)
            
            # 再次点击登录按钮（使用更平滑的移动）
            logger.info(f"移动鼠标到登录按钮: X={login_btn_x}, Y={login_btn_y}")
            pyautogui.moveTo(login_btn_x, login_btn_y, duration=1.0)
            time.sleep(0.5)
            pyautogui.click()
            logger.info("再次点击登录按钮") 
            time.sleep(8)
            
            # 更新当前URL
            current_url = get_current_url()
            logger.info(f"验证当前URL: {current_url}")
            captcha_attempts += 1
        
        # 检查是否需要邮箱验证码验证
        if "email-verification" in current_url:
            logger.info("检测到需要邮箱验证码验证")
            logger.info(f"当前URL: {current_url}")
            
            # 使用智能邮箱验证系统获取验证码
            logger.info("正在自动获取邮箱验证码...")
            verification_code = get_verification_code_smart(email, max_attempts=30, delay=5)
            
            if not verification_code:
                logger.error("无法获取邮箱验证码，登录失败")
                return False
            
            logger.info(f"成功获取验证码: {verification_code}")
            
            # 获取验证码输入框坐标
            code_input_x, code_input_y = current_pc.get_coord("VERIFICATION_CODE_INPUT")
            
            # 点击验证码输入框
            logger.info(f"移动鼠标到验证码输入框: X={code_input_x}, Y={code_input_y}")
            pyautogui.moveTo(code_input_x, code_input_y, duration=1.0)
            time.sleep(0.5)
            pyautogui.click()
            time.sleep(1)
            
            # 复制验证码到剪贴板并粘贴
            pyperclip.copy(verification_code)
            logger.info("正在粘贴验证码...")
            pyautogui.hotkey(MODIFIER, 'v')
            time.sleep(2)
            
            # 等待验证码验证完成
            logger.info("等待邮箱验证码验证完成...")
            time.sleep(8)
            
            # 更新当前URL，检查验证是否成功
            current_url = get_current_url()
            logger.info(f"邮箱验证后当前URL: {current_url}")
        
        # 最终检查登录结果
        if not "www.cursor.com" in current_url:
            logger.error(f"登录失败，当前URL: {current_url}")
            return False
        # 此时 浏览器打开 https://www.cursor.com/cn/dashboard
        #  然后5秒钟后 获取当前链接，如果链接为 https://www.cursor.com/cn/dashboard 则登录成功，否则登陆失败
        time.sleep(2)
        webbrowser.open("https://www.cursor.com/cn/dashboard")
        time.sleep(5)
        final_url = get_current_url()
        if "www.cursor.com/cn/dashboard" in final_url:
            logger.info("登录成功，已跳转到Cursor设置页面")
        else:
            logger.error(f"登录失败，当前URL: {final_url}")
            return False
        logger.info("登录成功，已跳转到Cursor设置页面")
        # 点击 Settings 按钮 
        logger.info(f"移动鼠标到Settings按钮: X={settings_btn_x}, Y={settings_btn_y}")
        pyautogui.moveTo(settings_btn_x, settings_btn_y, duration=1.0)
        time.sleep(0.5)
        pyautogui.click()
        logger.info("点击Settings按钮")
        time.sleep(3)

        # 点击 Advanced Account Settings 按钮
        logger.info(f"移动鼠标到Advanced Account Settings按钮: X={advanced_btn_x}, Y={advanced_btn_y}")
        pyautogui.moveTo(advanced_btn_x, advanced_btn_y, duration=1.0)
        time.sleep(0.5)
        pyautogui.click()
        logger.info("点击Advanced Account Settings按钮")
        time.sleep(2)

        # 点击 Delete Account 按钮
        logger.info(f"移动鼠标到Delete Account按钮: X={delete_account_x}, Y={delete_account_y}")
        pyautogui.moveTo(delete_account_x, delete_account_y, duration=1.0)
        time.sleep(0.5)
        pyautogui.click()
        logger.info("点击Delete Account按钮")
        time.sleep(2)

        # 点击 Delete Account 输入框
        logger.info(f"移动鼠标到Delete Account确认输入框: X={delete_confirm_x}, Y={delete_confirm_y}")
        pyautogui.moveTo(delete_confirm_x, delete_confirm_y, duration=1.0)
        time.sleep(0.5)
        pyautogui.click()
        logger.info("点击Delete Account确认输入框")
        time.sleep(0.5)

        # 输入 delete
        pyautogui.write("delete", interval=0.1)
        logger.info("输入确认文字: delete")
        time.sleep(1)

        # 点击 Delete 按钮
        logger.info(f"移动鼠标到最终Delete按钮: X={delete_btn_x}, Y={delete_btn_y}")
        pyautogui.moveTo(delete_btn_x, delete_btn_y, duration=1.0)
        pyautogui.click()
        logger.info("点击最终Delete按钮")

        # 等待3秒，获取当前链接  如果是 https://www.cursor.com/cn，则表示账号删除成功，然后就关闭浏览器
        time.sleep(3)
        final_url = get_current_url()
        logger.info(f"删除操作后当前URL: {final_url}")
        
        if "www.cursor.com" in final_url and "/dashboard" not in final_url:
            logger.info("账号删除成功，即将关闭浏览器")
            # 关闭浏览器
            close_browser()
            return True
        else:
            logger.error(f"账号删除失败，当前URL: {final_url}")
            return False
            
    except Exception as e:
        logger.error(f"删除账号时出错: {e}")
        return False

# 新增一个方法，用于请求 一个接口， filter-and-increment，，具体查看api-docs.json文件。
# 根据该接口返回的数据，拿里面的cachedEmail，然后去数据库中查询，该数据库是cursor_auth_store.db，表是cursor_auth，拿到该email对应的记录
# 然后执行删除账号操作，，如果没找到，5秒后继续请求该接口，直到找到为止。

def get_account_from_api_and_delete():
    """
    从API接口获取账号信息并执行删除操作
    """
    # api_url = "http://localhost:8080/cursor-api/api/tokens/filter-and-increment"
    api_url = "http://************:8080/cursor-api/api/tokens/filter-and-increment"
    
    # API请求参数 - 这些参数需要根据实际情况配置
    params = {
        "myAuthCode": "yunzhongauth1996",  # 需要配置实际的认证码
        "deleteCount": 0,  # 删除次数
        "registrationCount": 1  # 注册次数
    }
    
    logger.info("开始从API接口获取账号信息...")
    
    while True:
        try:
            # 请求API接口
            logger.info(f"请求API接口: {api_url}")
            response = requests.get(api_url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"API响应: {data}")
                
                # 检查响应格式 - API返回code为200表示成功
                if data.get("code") == 200 and data.get("data"):
                    token_data = data["data"]
                    cached_email = token_data.get("cachedEmail")
                    
                    if cached_email:
                        logger.info(f"从API获取到邮箱: {cached_email}")
                        
                        # 在数据库中查询该邮箱对应的记录
                        account = get_account_by_email(cached_email)
                        
                        
                        if account:
                            logger.info(f"在数据库中找到邮箱 {cached_email} 对应的记录")
                            # 这里 立即把 数据保存到，cursor_account_delete_record表里面，此时的delete_count 设置为0
                            save_initial_delete_record(cached_email)
                            
                            # 执行删除账号操作
                            success = delete_cursor_account(account["email"], account["password"])
                            
                            if success:
                                # 更新删除记录
                                update_delete_record(account["email"])
                                logger.info(f"账号 {account['email']} 删除成功并已记录")
                                return True
                            else:
                                logger.error(f"账号 {account['email']} 删除失败")
                                return False
                        else:
                            logger.warning(f"数据库中未找到邮箱 {cached_email} 对应的记录")
                    else:
                        logger.warning("API响应中未找到cachedEmail字段，停止请求")
                        return False
                elif data.get("code") == 200 and not data.get("data"):
                    logger.info("API返回空数据，没有可用的账号信息，停止请求")
                    return False
                else:
                    logger.warning(f"API响应格式异常: {data}")
                    if data.get("code") != 200:
                        logger.error(f"API返回错误码: {data.get('code')}, 消息: {data.get('message', '未知错误')}")
                        return False
            else:
                logger.error(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
                return False
                
        except requests.RequestException as e:
            logger.error(f"API请求异常: {e}")
            return False
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return False
        except Exception as e:
            logger.error(f"处理API响应时出错: {e}")
            return False
        
        # 等待5秒后继续请求
        logger.info("等待5秒后继续请求API接口...")
        time.sleep(5)

def get_account_by_email(email):
    """
    根据邮箱从数据库获取账号信息
    """
    conn = connect_db()
    try:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT email, password 
            FROM cursor_auth
            WHERE email = ?
        """, (email,))
        
        result = cursor.fetchone()
        if result:
            return {"email": result['email'], "password": result['password']}
        else:
            logger.info(f"数据库中未找到邮箱: {email}")
            return None
    except sqlite3.Error as e:
        logger.error(f"查询账号失败: {e}")
        return None
    finally:
        conn.close()

def main_with_api(executions=1, interval=30, max_failures=3):
    """
    使用API接口的主函数 - 支持多次执行和失败重试
    
    参数:
    executions: 执行次数，默认1次
    interval: 每次执行之间的间隔(秒)，默认30秒
    max_failures: 最大连续失败次数，超过则停止执行，默认3次
    """
    logger.info("=" * 60)
    logger.info(f"Cursor账号删除脚本 - 计划执行 {executions} 次，间隔 {interval} 秒")
    logger.info("=" * 60)
    
    # 创建删除记录表
    create_delete_record_table()
    
    consecutive_failures = 0  # 连续失败计数
    successful_runs = 0       # 成功执行计数
    total_attempts = 0        # 总尝试次数
    
    for run_number in range(1, executions + 1):
        logger.info("=" * 40)
        logger.info(f"开始第 {run_number}/{executions} 次删除操作")
        logger.info("=" * 40)
        
        total_attempts += 1
        
        try:
            # 从API获取账号并执行删除
            logger.info(f"第 {run_number} 次尝试：开始从API获取账号信息并执行删除...")
            success = get_account_from_api_and_delete()
            
            if success:
                consecutive_failures = 0  # 重置连续失败计数
                successful_runs += 1
                logger.info(f"✅ 第 {run_number} 次删除操作成功完成")
            else:
                consecutive_failures += 1
                logger.error(f"❌ 第 {run_number} 次删除操作失败")
                logger.error(f"连续失败次数: {consecutive_failures}/{max_failures}")
                
                # 如果连续失败次数达到上限，停止执行
                if consecutive_failures >= max_failures:
                    logger.critical(f"达到最大连续失败次数({max_failures})，停止执行")
                    break
            
        except Exception as e:
            consecutive_failures += 1
            logger.error(f"❌ 第 {run_number} 次执行时发生异常: {str(e)}")
            logger.error(f"连续失败次数: {consecutive_failures}/{max_failures}")
            
            # 如果连续失败次数达到上限，停止执行
            if consecutive_failures >= max_failures:
                logger.critical(f"达到最大连续失败次数({max_failures})，停止执行")
                break
        
        # 如果不是最后一次执行，等待指定的间隔时间
        if run_number < executions:
            logger.info(f"等待 {interval} 秒后开始下一次删除操作...")
            time.sleep(interval)
    
    # 执行统计
    logger.info("=" * 60)
    logger.info("删除操作统计:")
    logger.info(f"计划执行次数: {executions}")
    logger.info(f"实际执行次数: {total_attempts}")
    logger.info(f"成功次数: {successful_runs}")
    logger.info(f"失败次数: {total_attempts - successful_runs}")
    logger.info(f"成功率: {(successful_runs/total_attempts*100):.1f}%" if total_attempts > 0 else "0%")
    logger.info("=" * 60)
    logger.info("删除脚本执行完毕。")

def main():
    """原有的主函数"""
    logger.info("开始Cursor账号删除流程")
    
    # 创建删除记录表
    create_delete_record_table()
    
    # 获取要删除的账号
    account = get_account_to_delete()
    if not account:
        logger.info("没有需要删除的账号，程序退出")
        return
    
    email = account["email"]
    password = account["password"]
    logger.info(f"准备删除账号: {email}")
    
    # 执行删除账号操作
    success = delete_cursor_account(email, password)
    
    # 如果删除成功，记录到数据库
    if success:
        update_delete_record(email)
        logger.info(f"账号 {email} 删除成功并已记录")
    else:
        logger.error(f"账号 {email} 删除失败")

if __name__ == "__main__":
    # 默认参数
    executions = 300       # 默认执行10次
    interval = 20         # 每次间隔30秒
    max_failures = 10      # 最大连续失败3次
    
    # 从命令行参数获取配置(如果提供)
    if len(sys.argv) > 1:
        try:
            executions = int(sys.argv[1])
            if len(sys.argv) > 2:
                interval = int(sys.argv[2])
            if len(sys.argv) > 3:
                max_failures = int(sys.argv[3])
        except ValueError:
            print("参数格式错误。正确格式: python delete_cursor_account.py [执行次数] [间隔秒数] [最大失败次数]")
            sys.exit(1)
    
    print(f"删除参数配置:")
    print(f"- 执行次数: {executions}")
    print(f"- 间隔时间: {interval}秒")
    print(f"- 最大连续失败次数: {max_failures}")
    print("-" * 40)
    
    # 可以选择使用哪种方式
    # main()  # 原有的方式
    main_with_api(executions, interval, max_failures)  # 新的API方式
