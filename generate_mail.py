"""
邮箱生成器 (带数据库持久化)
-----------------------

这个脚本实现了一个邮箱生成器，可以按照特定规则生成随机邮箱地址。
所有状态都会保存在cursor_auth_store.db数据库中，确保程序重启后能够继续上次的状态。

主要功能：
1. 按照3:1的比例从pool_a和pool_b两个邮箱池中获取邮箱数据
2. 从pool_a中获取邮箱时，确保均衡使用所有配置
3. 从pool_b中获取邮箱时，不仅要均衡使用所有配置，还要检查使用次数是否达到上限
4. 所有状态保存到数据库中，确保程序重启后能继续之前的状态

使用方式：
1. 首次使用前调用 create_tables() 创建数据库表
2. 使用 generate_emails(n) 生成n个邮箱

作者：AI助手
日期：2024
"""

import random
import string
import sqlite3
import os

# 数据库配置
DB_FILE = "cursor_auth_store.db"

# 邮箱配置 - 格式: '完整邮箱'
pool_a = ['<EMAIL>']                   # 池A中的邮箱将循环使用
pool_b = ['<EMAIL>']     # 池B中的邮箱将循环使用，但受限制表约束

def parse_email(email):
    """解析邮箱，分离前缀和后缀"""
    parts = email.split('@')
    if len(parts) == 2:
        prefix = parts[0]
        suffix = '@' + parts[1]
        return prefix, suffix
    return None, email  # 如果解析失败，将整个值视为后缀

def create_tables():
    """
    创建并初始化数据库表
    
    这个函数应该只在第一次设置时调用
    """
    # 连接数据库
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    # 检查表是否存在，如果存在则先删除
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='suffix_usage'")
    if cursor.fetchone():
        cursor.execute("DROP TABLE suffix_usage")
        
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='generator_state'")
    if cursor.fetchone():
        cursor.execute("DROP TABLE generator_state")
    
    # 创建suffix_usage表
    print("创建suffix_usage表...")
    cursor.execute('''
    CREATE TABLE suffix_usage (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        prefix TEXT NOT NULL,
        suffix TEXT NOT NULL,
        total_count INTEGER NOT NULL DEFAULT 0,
        max_count INTEGER,
        pool_type TEXT NOT NULL
    )
    ''')
    
    # 填充pool_a邮箱数据
    for email in pool_a:
        prefix, suffix = parse_email(email)
        cursor.execute('''
        INSERT INTO suffix_usage (prefix, suffix, total_count, max_count, pool_type)
        VALUES (?, ?, 0, NULL, 'A')
        ''', (prefix, suffix))
    
    # 填充pool_b邮箱数据，设置**********初始值为6
    for email in pool_b:
        prefix, suffix = parse_email(email)
        initial_count = 6 if email == '<EMAIL>' else 0
        cursor.execute('''
        INSERT INTO suffix_usage (prefix, suffix, total_count, max_count, pool_type)
        VALUES (?, ?, ?, 10, 'B')
        ''', (prefix, suffix, initial_count))
    
    # 创建generator_state表
    print("创建generator_state表...")
    cursor.execute('''
    CREATE TABLE generator_state (
        key_name TEXT PRIMARY KEY,
        value INTEGER NOT NULL
    )
    ''')
        
    # 初始化索引状态
    cursor.execute("INSERT INTO generator_state (key_name, value) VALUES ('last_index_pool_a', -1)")
    cursor.execute("INSERT INTO generator_state (key_name, value) VALUES ('last_index_pool_b', -1)")
    # 新增：池A连续使用计数，用于实现3:1的比例
    cursor.execute("INSERT INTO generator_state (key_name, value) VALUES ('pool_a_consecutive_usage', 0)")
    
    conn.commit()
    conn.close()
    
    print("数据库表创建并初始化完成")

def check_database():
    """
    检查数据库表是否存在
    
    如果表不存在，给出错误提示
    
    返回:
        bool: 表是否存在
    """
    if not os.path.exists(DB_FILE):
        print(f"错误: 数据库文件 {DB_FILE} 不存在，请先调用 create_tables() 创建数据库")
        return False
    
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    # 检查必要的表是否存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='suffix_usage'")
    suffix_usage_exists = cursor.fetchone() is not None
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='generator_state'")
    generator_state_exists = cursor.fetchone() is not None
    
    conn.close()
    
    if not suffix_usage_exists or not generator_state_exists:
        print("错误: 数据库中缺少必要的表，请先调用 create_tables() 创建表")
        return False
        
    return True

def load_state():
    """
    从数据库加载状态
    
    返回:
        tuple: (pool_a_data, pool_b_data, restriction_table, last_index_pool_a, last_index_pool_b, pool_a_consecutive_usage)
    """
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    # 构建后缀使用限制表
    restriction_table = {}
    
    # 加载pool_a数据
    cursor.execute("SELECT id, prefix, suffix, total_count FROM suffix_usage WHERE pool_type = 'A' ORDER BY id")
    pool_a_rows = cursor.fetchall()
    pool_a_data = []
    for row in pool_a_rows:
        id, prefix, suffix, total_count = row
        pool_a_data.append((id, prefix, suffix))
        # 将pool_a的数据也加入到限制表中
        restriction_table[id] = {'total_count': total_count, 'max_count': None}
    
    # 加载pool_b数据
    cursor.execute("SELECT id, prefix, suffix, total_count, max_count FROM suffix_usage WHERE pool_type = 'B' ORDER BY id")
    pool_b_rows = cursor.fetchall()
    
    # 添加pool_b数据到限制表
    pool_b_data = []
    for row in pool_b_rows:
        id, prefix, suffix, total_count, max_count = row
        pool_b_data.append((id, prefix, suffix))
        restriction_table[id] = {'total_count': total_count, 'max_count': max_count}
    
    # 加载索引状态
    cursor.execute("SELECT value FROM generator_state WHERE key_name = 'last_index_pool_a'")
    last_index_pool_a = cursor.fetchone()[0]
    
    cursor.execute("SELECT value FROM generator_state WHERE key_name = 'last_index_pool_b'")
    last_index_pool_b = cursor.fetchone()[0]
    
    # 新增：加载pool_a连续使用计数
    try:
        cursor.execute("SELECT value FROM generator_state WHERE key_name = 'pool_a_consecutive_usage'")
        result = cursor.fetchone()
        pool_a_consecutive_usage = result[0] if result else 0
    except:
        # 如果字段不存在，则添加它
        try:
            cursor.execute("INSERT INTO generator_state (key_name, value) VALUES ('pool_a_consecutive_usage', 0)")
            conn.commit()
            pool_a_consecutive_usage = 0
        except:
            pool_a_consecutive_usage = 0
    
    conn.close()
    
    return pool_a_data, pool_b_data, restriction_table, last_index_pool_a, last_index_pool_b, pool_a_consecutive_usage

def save_state(restriction_table, last_index_pool_a, last_index_pool_b, pool_a_consecutive_usage):
    """
    将状态保存到数据库
    
    参数:
        restriction_table (dict): 后缀使用限制表
        last_index_pool_a (int): pool_a的上次使用索引
        last_index_pool_b (int): pool_b的上次使用索引
        pool_a_consecutive_usage (int): pool_a连续使用次数
    """
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    # 更新后缀使用情况
    for id, data in restriction_table.items():
        cursor.execute('''
        UPDATE suffix_usage 
        SET total_count = ? 
        WHERE id = ?
        ''', (data['total_count'], id))
    
    # 更新索引状态
    cursor.execute("UPDATE generator_state SET value = ? WHERE key_name = 'last_index_pool_a'", (last_index_pool_a,))
    cursor.execute("UPDATE generator_state SET value = ? WHERE key_name = 'last_index_pool_b'", (last_index_pool_b,))
    
    # 更新pool_a连续使用计数
    try:
        cursor.execute("UPDATE generator_state SET value = ? WHERE key_name = 'pool_a_consecutive_usage'", (pool_a_consecutive_usage,))
    except:
        # 如果字段不存在，则添加它
        try:
            cursor.execute("INSERT INTO generator_state (key_name, value) VALUES ('pool_a_consecutive_usage', ?)", (pool_a_consecutive_usage,))
        except:
            print("更新pool_a_consecutive_usage失败")
    
    conn.commit()
    conn.close()

def generate_random_string(length=10):
    """
    生成随机字符串
    
    参数:
        length (int): 生成的字符串长度，默认为10
        
    返回:
        str: 由小写字母和数字组成的随机字符串
    """
    letters = string.ascii_lowercase + string.digits  # 包含所有小写字母和数字的字符集
    return ''.join(random.choice(letters) for _ in range(length))

def get_email_from_pool_a(pool_a_data, last_index_pool_a):
    """
    从pool_a中按顺序获取邮箱配置
    
    使用索引循环遍历pool_a中的所有配置，确保均衡使用
    
    参数:
        pool_a_data (list): 池A的数据
        last_index_pool_a (int): 上次使用的索引
    
    返回:
        tuple: (email, new_index, id) - 生成的邮箱、更新后的索引和使用的配置ID
    """
    # 如果pool_a为空，返回一个伪造的错误邮箱
    if not pool_a_data:
        return "<EMAIL>", last_index_pool_a, None
        
    # 更新索引，循环使用pool_a中的所有配置
    new_index = (last_index_pool_a + 1) % len(pool_a_data)
    id, prefix, suffix = pool_a_data[new_index]
    
    # 生成邮箱: 前缀 + 10位随机字符 + 后缀
    email = f"{prefix}{generate_random_string(10)}{suffix}"
    
    return email, new_index, id

def get_email_from_pool_b(pool_b_data, restriction_table, last_index_pool_b):
    """
    从pool_b中按顺序获取邮箱配置，确保符合限制
    
    参数:
        pool_b_data (list): 池B的数据
        restriction_table (dict): 使用限制表
        last_index_pool_b (int): 上次使用的索引
    
    返回:
        tuple: (email或None, new_index, id或None) - 生成的邮箱、更新后的索引和使用的配置ID
    """
    # 如果pool_b为空，直接返回None
    if not pool_b_data:
        return None, last_index_pool_b, None
        
    new_index = last_index_pool_b
    
    # 尝试查找符合条件的配置 - 最多尝试len(pool_b_data)次
    for i in range(len(pool_b_data)):
        # 更新索引，循环使用pool_b中的所有配置
        new_index = (new_index + 1) % len(pool_b_data)
        id, prefix, suffix = pool_b_data[new_index]
        
        # 检查是否满足限制条件 - 已使用次数必须小于最大允许次数
        if restriction_table[id]['total_count'] < restriction_table[id]['max_count']:
            # 生成邮箱: 前缀 + 10位随机字符 + 后缀
            email = f"{prefix}{generate_random_string(10)}{suffix}"
            return email, new_index, id
    
    # 如果池B中所有配置都已达到使用上限，返回None
    return None, new_index, None

def generate_emails(count=1):
    """
    生成指定数量的邮箱，默认只生成1个
    并保持3:1的比例从pool_a和pool_b取邮箱（跨调用维持）
    
    参数:
        count (int): 要生成的邮箱数量，默认为1
        
    返回:
        tuple: (list, int) - 生成的邮箱列表和最后一个生成邮箱的来源 (1 for pool_a, 2 for pool_b, 0 if none generated)
    """
    # 检查数据库表是否存在
    if not check_database():
        return [], 0 # 返回空的邮箱列表和默认来源值
    
    # 加载状态，包括新增的连续使用计数
    pool_a_data, pool_b_data, restriction_table, last_index_pool_a, last_index_pool_b, pool_a_consecutive_usage = load_state()
    
    print("初始状态:")
    print(f"last_index_pool_a: {last_index_pool_a}")
    print(f"last_index_pool_b: {last_index_pool_b}")
    print(f"pool_a连续使用次数: {pool_a_consecutive_usage}/3")
    print("restriction_table:")
    for id, data in restriction_table.items():
        print(f"  ID {id}: 已使用 {data['total_count']} / 最大 {data['max_count'] if data['max_count'] else '无限制'}")
    
    print("\n========== 开始生成邮箱 ==========")
    
    # 初始化
    generated_count = 0
    emails_generated = []  # 存储生成的邮箱
    last_email_source_variable = 0 # 存储最后一个邮箱的来源
    
    # 生成指定数量的邮箱（默认为1个）
    while generated_count < count:
        # 根据当前的连续使用计数决定使用哪个池
        # 自定义域名 专用
        if pool_a_consecutive_usage < 3 and False:
        # if pool_a_consecutive_usage < 3:
            # 从pool_a获取邮箱
            email, last_index_pool_a, used_id = get_email_from_pool_a(pool_a_data, last_index_pool_a)
            print(f"生成邮箱 (来自 Pool A): {email}")
            emails_generated.append(email)
            last_email_source_variable = 1 # 更新最后一个来源
            
            # 如果使用的ID有效，更新限制表
            if used_id:
                # 更新限制表 - 增加已使用次数
                restriction_table[used_id]['total_count'] += 1
                print(f"更新限制表: ID {used_id} 当前计数: {restriction_table[used_id]['total_count']}")
            
            # 增加连续使用计数
            pool_a_consecutive_usage += 1
            print(f"更新pool_a连续使用计数: {pool_a_consecutive_usage}/3")
            generated_count += 1
        else:
            # 尝试从pool_b获取邮箱
            email, last_index_pool_b, used_id = get_email_from_pool_b(pool_b_data, restriction_table, last_index_pool_b)
            if email:
                print(f"生成邮箱 (来自 Pool B): {email}")
                emails_generated.append(email)
                last_email_source_variable = 2 # 更新最后一个来源
                
                # 更新限制表 - 增加已使用次数
                restriction_table[used_id]['total_count'] += 1
                print(f"更新限制表: ID {used_id} 当前计数: {restriction_table[used_id]['total_count']}")
                
                # 重置连续使用计数
                pool_a_consecutive_usage = 0
                print("重置pool_a连续使用计数为0")
                generated_count += 1
            else:
                # 如果pool_b没有满足条件的邮箱，继续使用pool_a但不重置计数
                print("无法从pool_b获取符合条件的邮箱配置，使用pool_a代替")
                email, last_index_pool_a, used_id = get_email_from_pool_a(pool_a_data, last_index_pool_a)
                print(f"生成邮箱 (来自 Pool A): {email}")
                emails_generated.append(email)
                last_email_source_variable = 1 # 更新最后一个来源
                
                # 如果使用的ID有效，更新限制表
                if used_id:
                    # 更新限制表 - 增加已使用次数
                    restriction_table[used_id]['total_count'] += 1
                    print(f"更新限制表: ID {used_id} 当前计数: {restriction_table[used_id]['total_count']}")
                
                # 不重置连续使用计数，但也不增加，保持在3
                print(f"保持pool_a连续使用计数: {pool_a_consecutive_usage}/3")
                generated_count += 1
    
    # 打印最终的状态
    print("\n========== 最终状态 ==========")
    print(f"last_index_pool_a: {last_index_pool_a}")
    print(f"last_index_pool_b: {last_index_pool_b}")
    print(f"pool_a连续使用次数: {pool_a_consecutive_usage}/3")
    print("restriction_table:")
    for id, data in restriction_table.items():
        print(f"  ID {id}: 已使用 {data['total_count']} / 最大 {data['max_count'] if data['max_count'] else '无限制'}")
    
    # 保存状态到数据库
    save_state(restriction_table, last_index_pool_a, last_index_pool_b, pool_a_consecutive_usage)
    print("\n状态已保存到数据库")
    
    return emails_generated, last_email_source_variable

def update_restriction_table():
       # 连接数据库
   conn = sqlite3.connect("cursor_auth_store.db")
   cursor = conn.cursor()
   
   # 检查字段是否已存在
   cursor.execute("SELECT value FROM generator_state WHERE key_name = 'pool_a_consecutive_usage'")
   if cursor.fetchone() is None:
       # 添加新字段
       cursor.execute("INSERT INTO generator_state (key_name, value) VALUES ('pool_a_consecutive_usage', 0)")
       conn.commit()
       print("成功添加新字段")
   else:
       print("字段已存在")
       
   conn.close()
# 示例: 如何使用这个模块
if __name__ == "__main__":
    # 如果数据库表不存在，则创建表
    # update_restriction_table()
    
    print("哈哈")
    # # if not os.path.exists(DB_FILE) or not check_database():
    # #     create_tables()
    
    # # 生成10个邮箱
    # emails, last_source = generate_emails(10) # 修改：接收邮箱列表和最后一个来源
    # print(f"\n生成的邮箱列表: {emails}")
    # if emails: # 只有成功生成了邮箱，last_source才有意义
    #     source_name = "未知"
    #     if last_source == 1:
    #         source_name = "Pool A"
    #     elif last_source == 2:
    #         source_name = "Pool B"
    #     print(f"最后一个成功生成的邮箱的来源: {source_name} (代码: {last_source})")
    # else:
    #     print("没有生成任何邮箱。")