# 编写方法，用于在 cursor_account_delete_record表中 
# 获取  指定 delete_count  singup_count 的 邮箱, 按照 last_delete_time 按照 小到大排序, 每次只获取一个
# 获取成功后，修改记录在数据表的字段，，singup_count 加1.

import sqlite3
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置
DB_PATH = "augment_auth_store.db"

def connect_db():
    """连接到SQLite数据库"""
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row  # 启用行工厂，使结果可通过列名访问
        return conn
    except sqlite3.Error as e:
        logger.error(f"数据库连接失败: {e}")
        raise

def get_email_by_counts(delete_count, signup_count):
    """
    在cursor_account_delete_record表中获取指定delete_count和signup_count的邮箱
    按照last_delete_time从小到大排序，每次只获取一个
    获取成功后，将该记录的signup_count加1
    
    参数:
    delete_count: 指定的删除次数
    signup_count: 指定的注册次数
    
    返回:
    dict: 包含邮箱信息的字典，如果没有找到则返回None
    """
    conn = connect_db()
    cursor = conn.cursor()
    
    try:
        # 查询符合条件的记录，按last_delete_time从小到大排序，只取第一条
        cursor.execute("""
            SELECT id, email, delete_count, signup_count, last_delete_time, last_signup_time
            FROM cursor_account_delete_record 
            WHERE delete_count = ? AND signup_count = ?
            ORDER BY last_delete_time ASC NULLS FIRST
            LIMIT 1
        """, (delete_count, signup_count))
        
        result = cursor.fetchone()
        
        if result:
            # 获取记录信息
            record_id = result['id']
            email = result['email']
            current_signup_count = result['signup_count']
            
            logger.info(f"找到符合条件的邮箱: {email} (ID: {record_id})")
            logger.info(f"当前删除次数: {result['delete_count']}, 注册次数: {current_signup_count}")
            
            # 更新signup_count加1
            new_signup_count = current_signup_count + 1
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            cursor.execute("""
                UPDATE cursor_account_delete_record 
                SET signup_count = ?, last_signup_time = ?
                WHERE id = ?
            """, (new_signup_count, current_time, record_id))
            
            conn.commit()
            
            logger.info(f"成功更新记录 ID {record_id}: signup_count 从 {current_signup_count} 更新为 {new_signup_count}")
            
            # 返回邮箱信息
            return email
        else:
            logger.info(f"未找到符合条件的记录 (delete_count={delete_count}, signup_count={signup_count})")
            return None
            
    except sqlite3.Error as e:
        logger.error(f"数据库操作失败: {e}")
        conn.rollback()
        return None
    finally:
        conn.close()





def main():
    """主函数，演示功能使用"""
    logger.info("=" * 60)
    logger.info("cursor_account_delete_record 邮箱获取程序")
    logger.info("=" * 60)
    

    
    # 插入测试数据（可选）
    # insert_test_data()
    
    # 示例：获取delete_count=1, signup_count=1的邮箱
    logger.info("尝试获取 delete_count=1, signup_count=1 的邮箱...")
    result = get_email_by_counts(delete_count=1, signup_count=1)
    
    if result:
        logger.info(f"获取成功: {result}")
    else:
        logger.info("未找到符合条件的邮箱")

if __name__ == "__main__":
    main()