# 此文件 用于 同步数据
# 1. 首先 请求接口，获取黑名单 邮箱后缀
# 2. 将 黑名单 邮箱后缀 写入 数据库  cursor_auth_store.db 的 blacklist_email_suffix 表中
# 本地的黑名单表blacklist_email_suffix 只有id 和suffix
# 3. 把cursor_auth_store.db 里面的 数据表 cursor_auth 里面所有的数据 获取到，
# 4. 按照时间 排序， 一次性最多获取300 条数据，依次获取，要过滤掉 黑名单 邮箱后缀 的数据
# 对比时间,应该是更新时间字段， 要获取15天之内到数据，时间先后顺序，过滤被名单，过滤掉 is_upload 为1 的数据
#  把获取到的数据  放到列表中，请求接口，上传数据
#  上传数据成功后，把 is_upload 字段 设置为1
#  如果上传数据失败，把 is_upload 字段 设置为0

# 5. 如果上面第四步 需要在执行完毕后等待10秒，继续执行  直至 获取到的数据为空
  
#   获取黑名单接口 /cursor-api/api/email-blacklist/list
# 上传数据接口 /cursor-api/api/tokens/batch

import sqlite3
import requests
import json
import time
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API 配置
# BASE_URL = "http://localhost:8080/cursor-api"
BASE_URL = "http://************:8080/cursor-api"
EMAIL_BLACKLIST_URL = f"{BASE_URL}/api/email-blacklist/list"
TOKENS_BATCH_URL = f"{BASE_URL}/api/tokens/batch"

# 数据库配置
DB_PATH = "cursor_auth_store.db"

# 认证配置
AUTH_CODE = "yunzhongauth1996"  # 需要替换为实际的授权码

def connect_db():
    """连接到SQLite数据库"""
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row  # 启用行工厂，使结果可通过列名访问
        return conn
    except sqlite3.Error as e:
        logger.error(f"数据库连接失败: {e}")
        raise

def get_blacklist_from_api():
    """从API获取黑名单邮箱后缀列表"""
    try:
        response = requests.get(EMAIL_BLACKLIST_URL)
        response.raise_for_status()
        data = response.json()
        
        # 提取黑名单后缀 (根据实际API响应结构调整)
        blacklist = []
        if data and isinstance(data, dict) and 'records' in data:
            for item in data.get('records', []):
                if isinstance(item, dict) and 'suffix' in item:
                    blacklist.append(item['suffix'])
        
        logger.info(f"从API获取到{len(blacklist)}个黑名单邮箱后缀")
        return blacklist
    except requests.RequestException as e:
        logger.error(f"获取黑名单失败: {e}")
        return []

def update_blacklist_in_db(blacklist):
    """更新本地数据库中的黑名单"""
    conn = connect_db()
    cursor = conn.cursor()
    
    try:
        # 检查表是否存在，不存在则创建
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS blacklist_email_suffix (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                suffix TEXT NOT NULL UNIQUE
            )
        ''')
        
        # 清空现有黑名单
        cursor.execute("DELETE FROM blacklist_email_suffix")
        
        # 插入新的黑名单
        for suffix in blacklist:
            cursor.execute("INSERT INTO blacklist_email_suffix (suffix) VALUES (?)", (suffix,))
        
        conn.commit()
        logger.info(f"成功更新黑名单到数据库，共{len(blacklist)}条记录")
    except sqlite3.Error as e:
        conn.rollback()
        logger.error(f"更新黑名单失败: {e}")
    finally:
        conn.close()

def get_blacklist_from_db():
    """从数据库获取黑名单"""
    conn = connect_db()
    cursor = conn.cursor()
    
    try:
        cursor.execute("SELECT suffix FROM blacklist_email_suffix")
        return [row['suffix'] for row in cursor.fetchall()]
    except sqlite3.Error as e:
        logger.error(f"从数据库读取黑名单失败: {e}")
        return []
    finally:
        conn.close()

def extract_email_suffix(email):
    """提取邮箱后缀"""
    if email and '@' in email:
        return email.split('@')[1].lower()
    return None

def get_data_to_upload(batch_size=300):
    """
    从本地数据库获取待上传的数据
    - 最多获取batch_size条记录
    - 过滤掉黑名单邮箱后缀
    - 过滤掉已上传的数据
    - 获取15天内的数据
    """
    conn = connect_db()
    cursor = conn.cursor()
    
    try:
        # 获取黑名单后缀
        blacklist = get_blacklist_from_db()
        
        # 计算15天前的日期时间
        fifteen_days_ago = datetime.now() - timedelta(days=15)
        date_threshold = fifteen_days_ago.strftime('%Y-%m-%d %H:%M:%S')
        
        # 查询满足条件的记录（根据提供的表结构调整字段名）
        cursor.execute("""
            SELECT * FROM cursor_auth 
            WHERE (is_upload = 0 OR is_upload IS NULL) 
            AND updated_at >= ?
            ORDER BY updated_at ASC
            LIMIT ?
        """, (date_threshold, batch_size))
        
        rows = cursor.fetchall()
        
        # 将查询结果转为字典列表，并过滤掉黑名单邮箱后缀
        filtered_data = []
        original_ids = []  # 存储原始ID以便稍后更新状态
        
        for row in rows:
            data_dict = dict(row)
            email = data_dict.get('email')  # 使用数据库中实际的列名
            suffix = extract_email_suffix(email)
            
            # 如果邮箱后缀不在黑名单中，添加到结果列表
            if suffix and suffix not in blacklist:
                # 确保时间字段使用ISO格式 (将空格替换为T)
                created_at = data_dict.get('created_at')  # 使用数据库中实际的列名
                updated_at = data_dict.get('updated_at')  # 使用数据库中实际的列名
                
                if created_at and ' ' in created_at:
                    created_at = created_at.replace(' ', 'T')
                
                if updated_at and ' ' in updated_at:
                    updated_at = updated_at.replace(' ', 'T')
                
                # 存储原始ID
                original_ids.append(data_dict.get('id'))
                
                # 根据新的表结构创建完整的数据对象
                filtered_data.append({
                    # 注意：不发送本地数据库的id，避免与远程数据库ID冲突
                    # 'id': data_dict.get('id'),  # 不包含id，让远程服务基于邮箱判断
                    'cachedEmail': data_dict.get('email'),
                    'accessToken': data_dict.get('access_token'),
                    'refreshToken': data_dict.get('refresh_token'),
                    'cookies': data_dict.get('cookies') or '{}',
                    'machineId': data_dict.get('machine_id'),
                    'telemetryMachineId': data_dict.get('telemetry_machine_id'),
                    'envMachineId': data_dict.get('env_machine_id'),
                    'anonymizedId': data_dict.get('anonymized_id'),
                    'sessionId': data_dict.get('session_id'),
                    'agentId': data_dict.get('agent_id'),
                    'telemetryAnonId': data_dict.get('telemetry_anon_id'),
                    'telemetryMachineIdHash': data_dict.get('telemetry_machine_id_hash'),
                    'telemetrySessionId': data_dict.get('telemetry_session_id'),
                    'macMachineId': data_dict.get('mac_machine_id'),
                    'installationId': data_dict.get('installation_id'),
                    'storageServiceMachineId': data_dict.get('storage_service_machine_id'),
                    'telemetrySqmId': data_dict.get('telemetry_sqm_id'),
                    'telemetryDevDeviceId': data_dict.get('telemetry_dev_device_id'),
                    'isUsed': data_dict.get('is_used', False),
                    'createTime': created_at,
                    'updateTime': updated_at
                })
        
        logger.info(f"获取到{len(filtered_data)}条待上传数据")
        return filtered_data, original_ids
    except sqlite3.Error as e:
        logger.error(f"获取待上传数据失败: {e}")
        return [], []
    finally:
        conn.close()

def upload_data_to_api(data):
    """上传数据到API"""
    if not data:
        logger.info("没有数据需要上传")
        return True
    
    try:
        # 构造请求体
        payload = {
            "tokens": data
        }
        
        # 发送请求
        response = requests.post(
            TOKENS_BATCH_URL,
            json=payload,
            params={"myAuthCode": AUTH_CODE}
        )
        
        response.raise_for_status()
        result = response.json()
        
        # 记录完整的响应以便调试
        logger.info(f"API响应: {result}")
        
        # 检查响应中的消息是否包含'成功'关键词
        message = result.get('message', '')
        if '成功' in message or 'success' in message.lower():
            logger.info(f"上传数据成功，共{len(data)}条记录")
            return True
        elif result.get('code') == 0:  # 也检查code是否为0
            logger.info(f"上传数据成功，共{len(data)}条记录")
            return True
        else:
            logger.error(f"上传数据失败: {message}")
            return False
    except requests.RequestException as e:
        logger.error(f"上传数据请求异常: {e}")
        return False

def update_upload_status(ids, status):
    """更新本地数据库中的上传状态"""
    if not ids:
        return
    
    conn = connect_db()
    cursor = conn.cursor()
    
    try:
        # 构建IN子句参数占位符
        placeholders = ','.join('?' * len(ids))
        
        # 更新上传状态 (is_upload字段)
        cursor.execute(f"""
            UPDATE cursor_auth
            SET is_upload = ?
            WHERE id IN ({placeholders})
        """, [1 if status else 0] + ids)
        
        conn.commit()
        logger.info(f"成功更新{cursor.rowcount}条记录的上传状态为{status}")
    except sqlite3.Error as e:
        conn.rollback()
        logger.error(f"更新上传状态失败: {e}")
    finally:
        conn.close()

def sync_data():
    """主同步函数"""
    logger.info("开始数据同步...")
    
    # 1. 获取黑名单并更新到数据库
    blacklist = get_blacklist_from_api()
    update_blacklist_in_db(blacklist)
    
    # 2. 循环获取数据并上传
    while True:
        # 获取一批待上传数据
        data, original_ids = get_data_to_upload(300)
        
        # 如果没有数据，结束循环
        if not data:
            logger.info("没有更多数据需要同步，完成")
            break
        
        # 上传数据
        success = upload_data_to_api(data)
        
        # 更新上传状态
        update_upload_status(original_ids, success)
        
        # 如果上传失败，则停止同步
        if not success:
            logger.error("数据上传失败，终止同步过程。")
            break
        
        # 等待3秒后继续
        logger.info("等待3秒后继续...")
        time.sleep(3)

if __name__ == "__main__":
    try:
        sync_data()
    except Exception as e:
        logger.error(f"同步过程中发生未处理的异常: {e}")



