import webbrowser
import time
import pyautogui
import sys
import os
import subprocess
import pyperclip
import imaplib
import email
import re
import poplib
import sqlite3
import datetime
import logging
import logging.handlers
import random
import string
import json
import collections
from get_mail_code import get_2_code
from generate_mail import generate_emails
from sync_data import sync_data
from smart_email_verification import get_verification_code_smart
from get_email import get_email_by_counts
# 导入record_some.py中的功能
import record_some


# 初始状态 浏览器最大化，已进入直播间最大化， 已关注。

#  profile_area  坐标
# 关注按钮 坐标
# 取消关注确定坐标



def toggle_follow():
    # 鼠标移动到 个人头像区域
    pyautogui.moveTo(214, 199, duration=2)
    pyautogui.click()
    time.sleep(3)
    # 鼠标移动到 取消关注按钮
    pyautogui.moveTo(342, 335, duration=3)
    pyautogui.click()
    time.sleep(4)
    # 鼠标移动到 取消关注确定按钮
    pyautogui.moveTo(2155, 1170, duration=2)
    pyautogui.click()
    time.sleep(3)

    # 鼠标移动到 个人头像区域
    pyautogui.moveTo(214, 199, duration=3)
    pyautogui.click()
    time.sleep(5)

    # 鼠标移动到 关注按钮
    pyautogui.moveTo(342, 335, duration=1.5)
    pyautogui.click()
    time.sleep(3)

    # 移动到 评论输入框
    pyautogui.moveTo(3306, 2025, duration=1.5)
    pyautogui.click()
    time.sleep(1)
    pyautogui.typewrite(":")
    time.sleep(2)
    # 点击发送按钮
    pyautogui.moveTo(3794, 2027, duration=1.5)
    pyautogui.click()
    time.sleep(1)

if __name__ == "__main__":

    # 可指定该方法执行多少次，以及执行间隔时间
    print("开始执行,倒计时5秒")
    time.sleep(1)
    print("5")
    time.sleep(1)
    print("4")
    time.sleep(1)
    print("3")
    time.sleep(1)
    print("2")
    time.sleep(1)
    print("1")
    time.sleep(1)
    print("开始执行")
    for i in range(50):
        toggle_follow()
        print("第${i}次执行完毕")
        time.sleep(30)





