import pyautogui
import time

def show_position():
    """最简单的鼠标位置显示器"""
    print("****** 极简鼠标位置显示器 ******")
    print("按 Ctrl+C 退出程序")
    print()
    
    try:
        while True:
            # 获取当前鼠标位置
            x, y = pyautogui.position()
            # 显示位置信息
            info = f"鼠标位置: X={x}, Y={y}"
            # 使用退格符覆盖前一行
            print(info + " " * 10, end="\r")
            # 短暂延迟
            time.sleep(0.1)
    except KeyboardInterrupt:
        print("\n\n程序已退出")

if __name__ == "__main__":
    show_position() 