#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通过API获取Cursor Pro Token的自动化脚本
"""
import requests
import json
import time
import sqlite3
import datetime
import logging
import logging.handlers
import os
import sys
import random
import string
# 导入sync_data模块以支持数据上传
from sync_data import sync_data

# 日志设置
def setup_logging(log_directory="logs", max_size_mb=20):
    """
    设置日志记录
    
    参数:
    log_directory: 日志文件保存目录
    max_size_mb: 每个日志文件的最大大小(MB)
    
    返回值:
    logging.Logger: 配置好的日志记录器
    """
    # 创建日志目录(如果不存在)
    if not os.path.exists(log_directory):
        os.makedirs(log_directory)
    
    # 创建日志记录器
    logger = logging.getLogger("cursor_pro_api")
    logger.setLevel(logging.DEBUG)
    
    # 清除已有的处理器
    if logger.handlers:
        logger.handlers.clear()
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建文件处理器，支持日志滚动
    log_file = os.path.join(log_directory, "cursor_pro_api.log")
    file_handler = logging.handlers.RotatingFileHandler(
        log_file, 
        maxBytes=max_size_mb * 1024 * 1024,  # 转换为字节
        backupCount=10,  # 保留的日志文件数量
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    return logger

def generate_random_string(length=10):
    """
    生成指定长度的随机字符串
    
    参数:
    length: 字符串长度
    
    返回值:
    str: 随机字符串
    """
    chars = string.ascii_lowercase + string.digits
    return ''.join(random.choice(chars) for _ in range(length))

def get_cursor_pro_from_api(key, machine_code, version="2.1.1", platform="Windows", app_name="CursorPro", remark="官方版", logger=None):
    """
    从API获取Cursor Pro Token
    
    参数:
    key: API密钥
    machine_code: 机器码
    version: 版本号
    platform: 平台
    app_name: 应用名称
    remark: 备注
    logger: 日志记录器
    
    返回值:
    dict: 返回的账号信息和状态
    """
    if logger:
        logger.info("开始从API获取Cursor Pro Token")
    else:
        print("开始从API获取Cursor Pro Token")
    
    api_url = "http://14.103.190.198:5270/api/paid"
    payload = {
        "operation": "verify_paid",
        "key": key,
        "machine_code": machine_code,
        "version": version,
        "platform": platform,
        "app_name": app_name,
        "remark": remark
    }
    
    try:
        if logger:
            logger.info(f"向API发送请求: {api_url}")
            logger.info(f"请求参数: {payload}")
        else:
            print(f"向API发送请求: {api_url}")
            print(f"请求参数: {payload}")
        
        response = requests.post(api_url, json=payload, timeout=30)
        response.raise_for_status()  # 如果状态码不是200，将引发异常
        
        result = response.json()
        
        if logger:
            if result.get("success"):
                logger.info("API请求成功")
                logger.info(f"返回消息: {result.get('message')}")
                
                # 打印邮箱信息（屏蔽敏感信息）
                email = result.get("account_info", {}).get("邮箱", "")
                if email:
                    logger.info(f"获取到邮箱: {email}")
                
                # 打印令牌信息（只显示前10个字符）
                access_token = result.get("account_info", {}).get("访问令牌", "")
                refresh_token = result.get("account_info", {}).get("刷新令牌", "")
                
                if access_token:
                    logger.info(f"获取到访问令牌: {access_token[:10]}...")
                
                if refresh_token:
                    logger.info(f"获取到刷新令牌: {refresh_token[:10]}...")
            else:
                logger.error(f"API请求失败: {result.get('message', '未知错误')}")
        else:
            if result.get("success"):
                print("API请求成功")
                print(f"返回消息: {result.get('message')}")
                
                # 打印邮箱信息
                email = result.get("account_info", {}).get("邮箱", "")
                if email:
                    print(f"获取到邮箱: {email}")
                
                # 打印令牌信息（只显示前10个字符）
                access_token = result.get("account_info", {}).get("访问令牌", "")
                refresh_token = result.get("account_info", {}).get("刷新令牌", "")
                
                if access_token:
                    print(f"获取到访问令牌: {access_token[:10]}...")
                
                if refresh_token:
                    print(f"获取到刷新令牌: {refresh_token[:10]}...")
            else:
                print(f"API请求失败: {result.get('message', '未知错误')}")
        
        return result
    
    except requests.exceptions.RequestException as e:
        error_msg = f"API请求异常: {str(e)}"
        if logger:
            logger.error(error_msg)
        else:
            print(error_msg)
        return {"success": False, "message": error_msg}
    
    except Exception as e:
        error_msg = f"获取Cursor Pro Token时发生未预期的错误: {str(e)}"
        if logger:
            logger.error(error_msg)
        else:
            print(error_msg)
        return {"success": False, "message": error_msg}

def save_cursor_auth_to_sqlite(auth_info, db_path="cursor_auth_store.db", logger=None):
    """
    将Cursor认证信息保存到SQLite数据库中
    
    参数:
    auth_info: API返回的账号信息
    db_path: 目标SQLite数据库文件路径
    logger: 日志记录器
    
    返回值:
    bool: 是否成功保存
    dict: 包含生成的随机邮箱和令牌信息的字典
    """
    if logger:
        logger.info(f"正在将认证信息保存到数据库: {db_path}")
    else:
        print(f"正在将认证信息保存到数据库: {db_path}")
    
    try:
        # 从account_info中提取数据
        account_info = auth_info.get("account_info", {})
        # 生成一个12位小写字母和数字的gmail邮箱，而不是使用API返回的邮箱
        random_prefix = ''.join(random.choice(string.ascii_lowercase + string.digits) for _ in range(12))
        email = f"{random_prefix}@gmail.com"
        access_token = account_info.get("访问令牌", "")
        refresh_token = account_info.get("刷新令牌", "")
        
        # 获取当前时间
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 连接到SQLite数据库（如果不存在则创建）
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建表（如果不存在）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS cursor_auth (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT,
            is_upload INTEGER,
            access_token TEXT,
            refresh_token TEXT,
            created_at TEXT,
            updated_at TEXT
        )
        ''')
        
        # 检查此邮箱是否已经存在记录
        if email:
            cursor.execute("SELECT id FROM cursor_auth WHERE email = ?", (email,))
            existing_record = cursor.fetchone()
            
            if existing_record:
                # 更新现有记录
                record_id = existing_record[0]
                if logger:
                    logger.info(f"更新现有认证记录 ID: {record_id}")
                else:
                    print(f"更新现有认证记录 ID: {record_id}")
                
                cursor.execute('''
                UPDATE cursor_auth
                SET access_token = ?,
                    refresh_token = ?,
                    is_upload = ?,
                    updated_at = ?
                WHERE id = ?
                ''', (
                    access_token,
                    refresh_token,
                    0,  # 默认设置is_upload为0
                    current_time,
                    record_id
                ))
            else:
                # 插入新记录
                if logger:
                    logger.info("创建新的认证记录")
                else:
                    print("创建新的认证记录")
                
                cursor.execute('''
                INSERT INTO cursor_auth (
                    email, is_upload, access_token, refresh_token, 
                    created_at, updated_at
                )
                VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    email,
                    0,  # 默认设置is_upload为0
                    access_token,
                    refresh_token,
                    current_time,
                    current_time
                ))
        
        # 提交事务
        conn.commit()
        
        # 显示更新后的记录数量
        cursor.execute("SELECT COUNT(*) FROM cursor_auth")
        record_count = cursor.fetchone()[0]
        if logger:
            logger.info(f"数据库中现有 {record_count} 条认证记录")
        else:
            print(f"数据库中现有 {record_count} 条认证记录")
        
        conn.close()
        
        if logger:
            logger.info("认证信息已成功保存")
        else:
            print("认证信息已成功保存")
        
        # 返回包含生成的随机邮箱和令牌的字典
        result_info = {
            "email": email,
            "access_token": access_token,
            "refresh_token": refresh_token
        }
        
        return True, result_info
        
    except sqlite3.Error as e:
        error_msg = f"数据库操作错误: {str(e)}"
        if logger:
            logger.error(error_msg)
        else:
            print(error_msg)
        return False, {}
        
    except Exception as e:
        error_msg = f"保存认证信息时出现异常: {str(e)}"
        if logger:
            logger.error(error_msg)
        else:
            print(error_msg)
        return False, {}

def upload_data(logger=None):
    """
    上传数据到远程服务器
    
    参数:
    logger: 日志记录器
    
    返回值:
    bool: 是否成功上传
    """
    if logger:
        logger.info("开始上传数据...")
    else:
        print("开始上传数据...")
    
    try:
        # 调用sync_data模块中的同步功能
        sync_data()
        
        if logger:
            logger.info("数据上传完成")
        else:
            print("数据上传完成")
        
        return True
    except Exception as e:
        error_msg = f"数据上传失败: {str(e)}"
        if logger:
            logger.error(error_msg)
        else:
            print(error_msg)
        return False

def check_token_exists_in_db(token, db_path="cursor_auth_store.db", logger=None):
    """
    检查token是否已存在于cursor_auth表中
    
    参数:
    token: 要检查的访问令牌
    db_path: 数据库文件路径
    logger: 日志记录器
    
    返回值:
    bool: 如果token已存在则返回True，否则返回False
    """
    try:
        # 如果token为空，不需要检查
        if not token:
            return False
        
        # 连接到SQLite数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建表（如果不存在）确保查询不会失败
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS cursor_auth (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT,
            is_upload INTEGER,
            access_token TEXT,
            refresh_token TEXT,
            created_at TEXT,
            updated_at TEXT
        )
        ''')
        
        # 查询是否存在相同的token
        cursor.execute("SELECT COUNT(*) FROM cursor_auth WHERE access_token = ?", (token,))
        count = cursor.fetchone()[0]
        
        conn.close()
        
        return count > 0
        
    except Exception as e:
        error_msg = f"检查token时出错: {str(e)}"
        if logger:
            logger.error(error_msg)
        else:
            print(error_msg)
        return False

def main(executions=1, interval=60, max_attempts=3, retry_delay=20, log_max_size_mb=20):
    """
    主函数 - 支持多次执行和失败重试
    
    参数:
    executions: 执行次数
    interval: 每次执行之间的间隔(秒)
    max_attempts: 最大尝试次数
    retry_delay: 失败后重试的等待时间(秒)
    log_max_size_mb: 日志文件最大大小(MB)
    """
    # 设置日志记录
    logger = setup_logging(max_size_mb=log_max_size_mb)
    logger.info("=" * 60)
    logger.info(f"通过API获取Cursor Pro Token自动化脚本 - 计划执行 {executions} 次，间隔 {interval} 秒")
    logger.info("=" * 60)
    
    successful_runs = 0  # 成功执行的次数
    
    # 外部循环，控制执行次数
    for run_number in range(1, executions + 1):
        logger.info("=" * 50)
        logger.info(f"开始第 {run_number}/{executions} 次执行")
        logger.info("=" * 50)
        
        consecutive_failures = 0  # 连续失败计数
        attempt = 0              # 尝试次数
        
        while consecutive_failures < max_attempts and attempt < max_attempts * 3:  # 最多尝试max_attempts*3次
            attempt += 1
            logger.info("=" * 40)
            logger.info(f"第 {run_number} 次执行的第 {attempt} 次尝试")
            logger.info("=" * 40)
            
            try:
                # 生成随机key和machine_code，或者使用固定值
                # key = generate_random_string(10)
                # machine_code = generate_random_string(10)
                key = "0vvfwbzikl"  # 使用示例中提供的key
                machine_code = "Lb49bdXobT"  # 使用示例中提供的machine_code
                
                # 1. 从API获取Cursor Pro Token
                logger.info("1. 从API获取Cursor Pro Token")
                api_result = get_cursor_pro_from_api(key, machine_code, logger=logger)
                
                # 2. 检查API请求是否成功
                if not api_result.get("success"):
                    logger.error(f"API请求失败: {api_result.get('message', '未知错误')}")
                    consecutive_failures += 1
                    logger.error(f"连续失败次数: {consecutive_failures}/{max_attempts}")
                    
                    if consecutive_failures < max_attempts:
                        logger.info(f"等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                    continue
                
                # 3. 提取账号信息
                account_info = api_result.get("account_info", {})
                access_token = account_info.get("访问令牌", "")
                
                # 生成随机12位小写字母和数字的gmail邮箱
                random_prefix = ''.join(random.choice(string.ascii_lowercase + string.digits) for _ in range(12))
                random_email = f"{random_prefix}@gmail.com"
                logger.info(f"生成随机Gmail邮箱: {random_email}")
                
                # 4. 检查认证信息是否有效
                logger.info("2. 检查认证信息是否有效")
                if not access_token:
                    logger.error("获取到的访问令牌为空")
                    consecutive_failures += 1
                    logger.error(f"连续失败次数: {consecutive_failures}/{max_attempts}")
                    
                    if consecutive_failures < max_attempts:
                        logger.info(f"等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                    continue
                
                # 5. 检查token是否已存在
                logger.info("3. 检查访问令牌是否已存在于数据库中")
                if check_token_exists_in_db(access_token, logger=logger):
                    logger.error("访问令牌已存在于数据库中")
                    consecutive_failures += 1
                    logger.error(f"连续失败次数: {consecutive_failures}/{max_attempts}")
                    
                    if consecutive_failures < max_attempts:
                        logger.info(f"等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                    continue
                
                # 6. 保存认证信息到数据库
                logger.info("4. 保存认证信息到数据库")
                save_result, result_info = save_cursor_auth_to_sqlite(api_result, logger=logger)
                
                if not save_result:
                    logger.error("保存认证信息失败")
                    consecutive_failures += 1
                    logger.error(f"连续失败次数: {consecutive_failures}/{max_attempts}")
                    
                    if consecutive_failures < max_attempts:
                        logger.info(f"等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                    continue
                
                # 获取保存的随机邮箱和令牌信息
                saved_email = result_info.get("email", random_email)
                saved_access_token = result_info.get("access_token", access_token)
                
                # 7. 上传数据到远程服务器
                logger.info("5. 上传数据到远程服务器")
                upload_result = upload_data(logger=logger)
                
                if not upload_result:
                    logger.warning("数据上传失败，但不影响令牌获取")
                
                # 成功获取并保存
                logger.info("✅ 成功获取并保存Cursor Pro Token!")
                logger.info(f"随机Gmail邮箱: {saved_email}")
                logger.info(f"访问令牌: {saved_access_token[:10]}..." if saved_access_token else "访问令牌为空")
                
                # 重置连续失败计数
                consecutive_failures = 0
                successful_runs += 1
                
                # 成功获取后可以退出当前尝试循环
                break
                
            except Exception as e:
                consecutive_failures += 1
                logger.error(f"❌ 执行过程中出现异常: {str(e)}")
                logger.error(f"连续失败次数: {consecutive_failures}/{max_attempts}")
                
                if consecutive_failures < max_attempts:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
        
        # 如果不是最后一次执行，等待指定的间隔时间
        if run_number < executions:
            logger.info(f"等待 {interval} 秒后开始下一次执行...")
            time.sleep(interval)
    
    # 执行统计
    logger.info("=" * 60)
    logger.info("执行统计:")
    logger.info(f"计划执行次数: {executions}")
    logger.info(f"成功执行次数: {successful_runs}")
    logger.info(f"失败执行次数: {executions - successful_runs}")
    logger.info("=" * 60)
    logger.info("脚本执行完毕。")

if __name__ == "__main__":
    # 默认参数
    executions = 1          # 执行1次
    interval = 60           # 每次间隔60秒
    max_attempts = 3        # 最大连续失败3次
    retry_delay = 20        # 失败后等待20秒
    log_max_size_mb = 20    # 日志文件最大20MB
    
    # 从命令行参数获取配置(如果提供)
    if len(sys.argv) > 1:
        try:
            executions = int(sys.argv[1])
            if len(sys.argv) > 2:
                interval = int(sys.argv[2])
            if len(sys.argv) > 3:
                max_attempts = int(sys.argv[3])
            if len(sys.argv) > 4:
                retry_delay = int(sys.argv[4])
            if len(sys.argv) > 5:
                log_max_size_mb = int(sys.argv[5])
        except ValueError:
            print("参数格式错误。正确格式: python get_cursor_pro_from_api.py [执行次数] [间隔秒数] [最大失败次数] [重试延迟秒数] [日志大小MB]")
            sys.exit(1)
    
    # 执行主函数
    main(executions, interval, max_attempts, retry_delay, log_max_size_mb)