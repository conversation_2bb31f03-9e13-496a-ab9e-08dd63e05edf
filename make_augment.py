#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor程序自动化操作脚本
"""
import webbrowser
import time
import pyautogui
import sys
import os
import subprocess
import pyperclip
import imaplib
import email
import re
import poplib
import sqlite3
import datetime
import logging
import logging.handlers
import random
import string
import json
import collections
from get_mail_code import get_2_code
from generate_mail_for_augment import generate_emails
from sync_data import sync_data
from smart_email_verification_for_augment import get_verification_code_smart
from get_email_for_augment import get_email_by_counts
# 导入record_some.py中的功能
import record_some

# 全局变量，用于存储record_some.py生成的机器ID数据
MACHINE_IDS_DATA = {}

# 根据操作系统设置快捷键modifier
MODIFIER = 'command' if sys.platform == 'darwin' else 'ctrl'

#######################################################
# 邮箱域名配置 - 集中管理所有支持的邮箱域名
#######################################################

# 支持的邮箱域名列表 - 只在这里定义，其他地方引用这个变量
EMAIL_DOMAINS = [
    "ali88.xyz",
    # "ali877.xyz",
    # 添加新域名只需在此列表添加
]

# 获取所有支持的邮箱域名
def get_all_email_domains():
    """获取所有配置的邮箱域名"""
    return EMAIL_DOMAINS

#######################################################
# PC配置管理 - 不同电脑的配置统一管理
#######################################################

# PC配置类，用于存储每台电脑的坐标和路径设置
class PCConfig:
    def __init__(self, name, description=""):
        self.name = name
        self.description = description
        # 坐标配置
        self.coords = {}
        # 路径配置
        self.paths = {}
    
    def set_coords(self, coords_dict):
        """设置坐标配置"""
        self.coords.update(coords_dict)
        return self
    
    def set_paths(self, paths_dict):
        """设置路径配置"""
        self.paths.update(paths_dict)
        return self
    
    def get_coord(self, name, default=None):
        """获取坐标配置"""
        return self.coords.get(name, default)
    
    def get_path(self, name, default=None):
        """获取路径配置"""
        return self.paths.get(name, default)

# 创建不同电脑的配置
pc_configs = {}

# PC1 配置
pc1 = PCConfig("pc1", "MacBook Pro 13")
pc1.set_coords({

        # Cursor设置页面坐标
    "SETTINGS_LOGOUT_BUTTON": (662, 231),  # 登出按钮位置
    "SETTINGS_LOGIN_BUTTON": (865, 234),   # 登录按钮位置
    
    # 浏览器登录确认页面坐标
    "BROWSER_CONFIRM_LOGIN": (821, 617),   # 浏览器中的"确认登录"按钮位置
    
    # 验证网站页面坐标
    "AUTH_EMAIL_INPUT": (627, 479),        # 邮箱输入框位置
    "AUTH_SUBMIT_BUTTON": (636, 532),      # 邮箱提交按钮位置
    "AUTH_PASSWORD_INPUT": (627, 505),     # 密码输入框坐标
    "AUTH_PASSWORD_SUBMIT_BUTTON": (645, 564),    # 提交密码按钮位置
    # https://authenticator.cursor.sh/sign-up/password?first_name=&last_name=&email
    "AUTH_NEXT_BUTTON": (662, 679),        # 发送验证码按钮位置
    "AUTH_CAPTCHA_CHECKBOX": (636, 582),   # 人机验证勾选框位置
    "AUTH_CODE_INPUT": (605, 591),         # 验证码输入框位置


})
pc1.set_paths({
    "CURSOR_DB_PATH": '/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/state.vscdb',
    "LOG_DIRECTORY": "logs",
    "EMAIL_DB_PATH": "email_records.db",
    "AUTH_DB_PATH": "augment_auth_store.db",
})

# PC2 配置 (示例，复制PC1的配置并修改)
pc2 = PCConfig("pc2", "台式机Windows")
pc2.set_coords({
    # Cursor设置页面坐标
    "SETTINGS_LOGOUT_BUTTON": (1716, 329),  # 登出按钮位置
    "SETTINGS_LOGIN_BUTTON": (2114, 345),   # 登录按钮位置
    
    # 浏览器登录确认页面坐标
    "BROWSER_CONFIRM_LOGIN": (2007, 1001),   # 浏览器中的"确认登录"按钮位置
    
    # 验证网站页面坐标
    "AUTH_EMAIL_INPUT": (1706, 983),        # 邮箱输入框位置
    "AUTH_SUBMIT_BUTTON": (1746, 1084),      # 邮箱提交按钮位置
    "AUTH_PASSWORD_INPUT": (1730, 1031),     # 密码输入框坐标
    "AUTH_PASSWORD_SUBMIT_BUTTON": (1725, 1139),    # 提交密码按钮位置
    # https://authenticator.cursor.sh/sign-up/password?first_name=&last_name=&email
    "AUTH_NEXT_BUTTON": (662, 679),        # 发送验证码按钮位置
    "AUTH_CAPTCHA_CHECKBOX": (1709, 1158),   # 人机验证勾选框位置
    "AUTH_CODE_INPUT": (1661, 1168),         # 验证码输入框位置
})
pc2.set_paths({
    "CURSOR_DB_PATH": 'C:\\Users\\<USER>\\AppData\\Roaming\\Cursor\\User\\globalStorage\\state.vscdb',
    "LOG_DIRECTORY": "logs",
    "EMAIL_DB_PATH": "email_records.db",
    "AUTH_DB_PATH": "augment_auth_store.db",
})

# 注册PC配置
pc_configs["pc1"] = pc1
pc_configs["pc2"] = pc2

# 设置当前使用的PC配置
# 如果需要切换PC，只需修改这一行
current_pc_name = "pc2"  # 修改为需要使用的PC配置名称
current_pc = pc_configs[current_pc_name]

# 打印当前使用的配置
print(f"使用PC配置: {current_pc.name} - {current_pc.description}")

#######################################################
# 坐标配置区 - 从当前PC配置中获取坐标
#######################################################

# Cursor设置页面坐标
SETTINGS_LOGOUT_BUTTON_X, SETTINGS_LOGOUT_BUTTON_Y = current_pc.get_coord("SETTINGS_LOGOUT_BUTTON")
SETTINGS_LOGIN_BUTTON_X, SETTINGS_LOGIN_BUTTON_Y = current_pc.get_coord("SETTINGS_LOGIN_BUTTON")

# 浏览器登录确认页面坐标
BROWSER_CONFIRM_LOGIN_X, BROWSER_CONFIRM_LOGIN_Y = current_pc.get_coord("BROWSER_CONFIRM_LOGIN")

# 验证网站页面坐标
AUTH_EMAIL_INPUT_X, AUTH_EMAIL_INPUT_Y = current_pc.get_coord("AUTH_EMAIL_INPUT")
AUTH_SUBMIT_BUTTON_X, AUTH_SUBMIT_BUTTON_Y = current_pc.get_coord("AUTH_SUBMIT_BUTTON")
AUTH_PASSWORD_INPUT_X, AUTH_PASSWORD_INPUT_Y = current_pc.get_coord("AUTH_PASSWORD_INPUT")
AUTH_PASSWORD_SUBMIT_BUTTON_X, AUTH_PASSWORD_SUBMIT_BUTTON_Y = current_pc.get_coord("AUTH_PASSWORD_SUBMIT_BUTTON")
AUTH_NEXT_BUTTON_X, AUTH_NEXT_BUTTON_Y = current_pc.get_coord("AUTH_NEXT_BUTTON")
AUTH_CAPTCHA_CHECKBOX_X, AUTH_CAPTCHA_CHECKBOX_Y = current_pc.get_coord("AUTH_CAPTCHA_CHECKBOX")
AUTH_CODE_INPUT_X, AUTH_CODE_INPUT_Y = current_pc.get_coord("AUTH_CODE_INPUT")

# 如果需要修改坐标，只需更新PCConfig中的值

def close_cursor():
    """关闭Cursor程序"""
    print("关闭Cursor...")
    
    if sys.platform == 'darwin':
        # macOS方法不变
        os.system("osascript -e 'tell application \"Cursor\" to quit'")
        os.system("pkill -x Cursor")
    else:
        # Windows - 使用taskkill替代Alt+F4
        os.system("taskkill /f /im Cursor.exe 2>nul")
    
    print("等待Cursor完全关闭...")
    time.sleep(3)



def start_cursor():
    """
    启动Cursor程序
    
    返回值:
    bool: 是否成功启动Cursor
    """
    print("尝试启动Cursor...")
    
    try:
        if sys.platform == 'darwin':
            # macOS - 使用open命令启动应用
            os.system("open -a Cursor")
        else:
            # Windows - 使用正确的安装路径
            cursor_path = "D:\\softWare\\cursor\\Cursor.exe"
            subprocess.Popen([cursor_path])
            # 或者使用双引号解决路径中的空格问题
            # os.system('"D:\\softWare\\cursor\\Cursor.exe"')
        
        # 等待Cursor启动
        print("等待Cursor启动...")
        time.sleep(3)  # 可能需要更长的等待时间
        return True
    except Exception as e:
        print(f"启动Cursor失败: {str(e)}")
        return False

def maximize_cursor_window():
    """最大化当前窗口"""
    print("最大化窗口...")
    
    if sys.platform == 'darwin':
        print("使用Command+Control+F切换全屏模式...")
        try:
            # 单独按下和释放按键，正确的顺序是先command后control
            pyautogui.keyDown('command')
            time.sleep(0.5)
            pyautogui.keyDown('ctrl')  # 在pyautogui中，control键是'ctrl'
            time.sleep(0.5)
            pyautogui.press('f')
            time.sleep(0.5)
            pyautogui.keyUp('ctrl')
            time.sleep(0.5)
            pyautogui.keyUp('command')
            
            # 等待切换
            time.sleep(2)
        except Exception as e:
            print(f"快捷键切换失败: {str(e)}")
            
    else:
        # Windows - 使用Windows+Up快捷键
        pyautogui.hotkey('win', 'up')
    

def open_settings():
    """打开Cursor的设置页面"""
    print("正在打开设置页面...")
    # 点击 获取屏幕高度的一半    上下居中 然后靠左边界200的坐标
    # 获取屏幕高度的一半
    screen_height = pyautogui.size().height
    # 计算上下居中位置
    center_y = screen_height / 2
    # 计算靠左边界200的坐标
    left_x = 200
    pyautogui.click(left_x, center_y)

    
    # 使用快捷键组合打开设置
    if sys.platform == 'darwin':
        # 参照成功的最大化功能实现，使用Shift+Command+J
        print("使用Shift+Command+J打开设置...")
        try:
            # 重要：顺序应该是先按下modifier键(shift/command)，然后按下功能键(j)
            pyautogui.keyDown('shift')
            time.sleep(0.5)
            pyautogui.keyDown('command')
            time.sleep(0.5)
            pyautogui.press('j')
            time.sleep(0.5)
            pyautogui.keyUp('command')
            time.sleep(0.5)
            pyautogui.keyUp('shift')  # 修复：之前缺少了释放shift键
        except Exception as e:
            print(f"Shift+Command+J方法失败: {str(e)}")
    else:
        # Windows - 假设使用的是Ctrl+Shift+J或其他快捷键
        try:
            pyautogui.keyDown('ctrl')
            time.sleep(0.5)
            pyautogui.keyDown('shift') 
            time.sleep(0.5)
            pyautogui.press('j')
            time.sleep(0.5)
            pyautogui.keyUp('shift')
            time.sleep(0.5)
            pyautogui.keyUp('ctrl')
        except Exception as e:
            print(f"Windows方法失败: {str(e)}")
    
    # 等待设置页面打开
    time.sleep(2)

def logout():
    """登出Cursor"""
    print("登出Cursor...")
    pyautogui.moveTo(SETTINGS_LOGOUT_BUTTON_X, SETTINGS_LOGOUT_BUTTON_Y, duration=0)
    pyautogui.click()

def login():
    """登录Cursor"""
    print("登录Cursor...")
    pyautogui.moveTo(SETTINGS_LOGIN_BUTTON_X, SETTINGS_LOGIN_BUTTON_Y, duration=0)
    pyautogui.click()

def to_confirm_login():
    # 在浏览器中确认登录
    """确认登录"""
    print("确认登录...")
    pyautogui.moveTo(BROWSER_CONFIRM_LOGIN_X, BROWSER_CONFIRM_LOGIN_Y, duration=0.5)
    pyautogui.click()
    time.sleep(3)

def close_browser():
    """关闭浏览器"""
    print("关闭浏览器...")
    try:
        if sys.platform == 'darwin': # macOS
            # macOS logic remains the same
            browser_processes = ["Google Chrome", "Chrome", "chromedriver","SunBrowser"]
            for process in browser_processes:
                result = os.system(f"pkill {process}")
                if result == 0:
                    print(f"成功关闭{process}")
                    time.sleep(1)
                    return True
            result = os.system("killall 'Google Chrome'")
            if result == 0:
                print("成功关闭Google Chrome")
                time.sleep(1)
                return True
            print("在macOS上未找到或未能关闭浏览器进程")
            return False
        else: # Windows - Only the Alt+F4 method
            print("Attempting to close browser on Windows using 'open blank tab & Alt+F4' method...")
            exe_target = "chrome.exe"
            
            try:
                webbrowser.open('https://www.baidu.com/')
                time.sleep(2.5) # Wait for tab to open and browser to potentially gain focus
                pyautogui.hotkey('alt', 'f4') # Send Alt+F4
                time.sleep(3.0) # Wait for browser to process the close command

                # Verify if Chrome is still running
                is_still_running = True # Assume it's running until proven otherwise
                try:
                    # Use subprocess.run to check for the process
                    # /NH for No Header, /FI for Filter
                    result = subprocess.run(
                        ['tasklist', '/NH', '/FI', f'IMAGENAME eq {exe_target}'],
                        capture_output=True, text=True, errors='ignore', check=False, timeout=5
                    )
                    # If tasklist ran successfully (returncode 0) and chrome.exe is NOT in output, it's closed.
                    # If chrome.exe IS in output, it's still running.
                    if result.returncode == 0:
                        if exe_target.lower() not in result.stdout.lower():
                            is_still_running = False # Not found in tasklist output
                        else:
                            is_still_running = True # Found in tasklist output
                    else:
                        # tasklist might have failed to run or returned an error code
                        # (e.g., if no processes match filter, it might still be returncode 0 but empty stdout,
                        # or return non-zero if filter is invalid, etc.)
                        # For simplicity, if tasklist doesn't clearly show it's gone, assume it might be running.
                        print(f"Tasklist check for {exe_target} was inconclusive (return code: {result.returncode}). Assuming it might still be running.")
                        is_still_running = True

                except subprocess.TimeoutExpired:
                    print(f"Tasklist verification timed out. Assuming {exe_target} might still be running.")
                    is_still_running = True
                except FileNotFoundError:
                    print("Tasklist command not found. Cannot verify browser closure status.")
                    is_still_running = True # Cannot verify, assume it's not definitively closed by this method.
                except Exception as e_verify:
                    print(f"Error during tasklist verification: {e_verify}. Assuming {exe_target} might still be running.")
                    is_still_running = True

                if not is_still_running:
                    print(f"Browser ({exe_target}) appears to be closed after Alt+F4 method.")
                    time.sleep(1)
                    return True
                else:
                    print(f"Browser ({exe_target}) is still running or closure could not be verified after Alt+F4 method.")
                    return False

            except Exception as e_method:
                print(f"Error during 'open blank tab & Alt+F4' method: {str(e_method)}")
                return False # Method failed

    except Exception as e_main:
        print(f"关闭浏览器时出现主错误: {str(e_main)}")
        return False



def get_current_url():
    """
    获取浏览器当前URL的方法
    
    返回值:
    str: 当前浏览器地址栏中的URL
    """
    print("正在获取当前链接地址...")
    
    # 保存原始剪贴板内容
    original_clipboard = pyperclip.paste()
    
    # 使用快捷键复制当前URL (Command+L 选中地址栏, 然后 Command+C 复制)
    pyautogui.hotkey(MODIFIER, 'l')  # 选中地址栏
    time.sleep(0.5)
    pyautogui.hotkey(MODIFIER, 'c')  # 复制地址
    time.sleep(0.5)
    
    # 获取剪贴板中的URL
    current_url = pyperclip.paste()
    
    # 恢复原始剪贴板内容
    pyperclip.copy(original_clipboard)
    
    print(f"获取到的当前URL: {current_url}")
    
    return current_url

def check_url_success(url):

    success_marker = "https://app.augmentcode.com/account/subscription"
    if url.startswith(success_marker):
        return True
    return False

def maximize_browser_window():
    """最大化浏览器窗口"""
    print("最大化浏览器窗口...") # 更新提示信息
    if sys.platform == 'darwin':  # macOS
        # 使用Command+Control+F切换全屏
        pyautogui.hotkey('command', 'ctrl', 'f')
    else:
        # Windows - 使用Windows+Up快捷键进行最大化
        pyautogui.hotkey('win', 'up')

def wait_for_verification_email_pop3(email_address, password, target_recipient="", max_attempts=30, delay=10, pop3_server="pop.163.com", pop3_port=995):
    """
    使用POP3协议等待并获取验证码邮件
    
    参数:
    email_address: 邮箱地址
    password: 邮箱密码
    target_recipient: 目标收件人地址（用于匹配转发邮件）
    max_attempts: 最大尝试次数
    delay: 每次检查的间隔时间(秒)
    pop3_server: POP3服务器地址
    pop3_port: POP3服务器端口
    
    返回值:
    str: 验证码，如果没有找到则返回None
    """
    print(f"等待验证码邮件发送到 {email_address}...")
    print(f"目标收件人地址为: {target_recipient}")
    
    for attempt in range(max_attempts):
        print(f"第 {attempt+1}/{max_attempts} 次检查新邮件...")
        
        try:
            # 连接到POP3服务器
            print("\n正在连接到POP3服务器...")
            pop_conn = poplib.POP3_SSL(pop3_server, pop3_port)
            
            # 身份验证
            print("正在登录邮箱...")
            pop_conn.user(email_address)
            login_response = pop_conn.pass_(password)
            print(f"登录响应: {login_response}")
            
            # 获取邮件统计信息
            stat_response = pop_conn.stat()
            print(f"邮件统计: 邮件数量 = {stat_response[0]}, 总大小 = {stat_response[1]} 字节")
            
            # 如果没有邮件，继续下一次尝试
            if stat_response[0] == 0:
                print("邮箱中没有邮件")
                pop_conn.quit()
                if attempt < max_attempts - 1:
                    print(f"未找到验证码邮件，{delay}秒后重试...")
                    time.sleep(delay)
                continue
            
            # 获取所有邮件索引
            resp, mails, octets = pop_conn.list()
            
            # 从最新邮件开始检查，最新的邮件索引最大
            mail_indices = [int(mail.split()[0]) for mail in mails]
            mail_indices.sort(reverse=True)  # 从大到小排序，最新的邮件先检查
            
            # 遍历最新的邮件（最多检查20封）
            for mail_index in mail_indices[:10]:
                # 获取这封邮件
                response, lines, octets = pop_conn.retr(mail_index)
                
                # 将邮件内容转换为一个字符串
                message_content = b'\n'.join(lines)
                
                # 解析邮件内容
                msg = email.message_from_bytes(message_content)
                
                # 提取基本信息
                subject = msg.get('subject', '')
                from_addr = msg.get('from', '')
                to_addr = msg.get('to', '')
                date = msg.get('date', '')
                
                # 提取邮件正文
                body = ""
                if msg.is_multipart():
                    for part in msg.walk():
                        content_type = part.get_content_type()
                        content_disposition = str(part.get("Content-Disposition"))
                        
                        # 跳过附件
                        if "attachment" in content_disposition:
                            continue
                        
                        if content_type == "text/plain" and "attachment" not in content_disposition:
                            try:
                                part_body = part.get_payload(decode=True)
                                # 尝试不同的编码方式解码
                                encodings = ['utf-8', 'gb2312', 'gbk', 'gb18030', 'big5']
                                for enc in encodings:
                                    try:
                                        body = part_body.decode(enc)
                                        break
                                    except UnicodeDecodeError:
                                        continue
                                if not body:
                                    body = part_body.decode('utf-8', errors='replace')
                                break
                            except Exception as e:
                                print(f"解码此部分时出错: {str(e)}")
                else:
                    try:
                        part_body = msg.get_payload(decode=True)
                        # 尝试不同的编码方式解码
                        encodings = ['utf-8', 'gb2312', 'gbk', 'gb18030', 'big5']
                        for enc in encodings:
                            try:
                                body = part_body.decode(enc)
                                break
                            except UnicodeDecodeError:
                                continue
                        if not body:
                            body = part_body.decode('utf-8', errors='replace')
                    except Exception as e:
                        print(f"解码邮件正文时出错: {str(e)}")
                
                # 查找验证码 - 使用多种模式匹配
                verification_code = None
                
                # 尝试匹配Cursor邮件中常见的验证码格式
                # 1. 标准6位连续数字
                code_match = re.search(r'\b\d{6}\b', body)
                if code_match:
                    verification_code = code_match.group(0)
                    print(f"找到连续6位数字验证码: {verification_code}")
                # 2. 带空格的6位数字 (如 "1 2 3 4 5 6")
                else:
                    # 寻找包含数字和空格的行
                    spaced_code_match = re.search(r'[\d\s]{9,17}', body)
                    if spaced_code_match:
                        spaced_code = spaced_code_match.group(0).strip()
                        # 清理空格，提取纯数字
                        cleaned_code = clean_verification_code(spaced_code)
                        # 检查是否得到了6位数字
                        if len(cleaned_code) == 6:
                            verification_code = cleaned_code
                            print(f"找到带空格的验证码: '{spaced_code}'")
                            print(f"清理后的验证码: {verification_code}")
                    # 3. 尝试查找包含在引号或者其他文本标记中的6位数字
                    else:
                        code_context_match = re.search(r'code\s+is:?\s*[\'"]?(\d[\d\s]+\d)[\'"]?', body, re.IGNORECASE)
                        if code_context_match:
                            context_code = code_context_match.group(1).strip()
                            cleaned_code = clean_verification_code(context_code)
                            if len(cleaned_code) == 6:
                                verification_code = cleaned_code
                                print(f"从上下文中找到验证码: '{context_code}'")
                                print(f"清理后的验证码: {verification_code}")
                
                # 在转发邮件中查找原始收件人
                original_recipient = None
                
                # 首先，如果有目标收件人，直接在邮件正文和各种信息中查找完全匹配的目标收件人
                if target_recipient:
                    print(f"需要精确匹配的目标收件人: {target_recipient}")
                    # 直接在to_addr中查找
                    if target_recipient == to_addr:
                        print(f"收件人字段完全匹配目标收件人: {to_addr}")
                        original_recipient = target_recipient
                    # 在邮件正文中查找完全匹配的收件人
                    elif target_recipient in body and re.search(rf'\b{re.escape(target_recipient)}\b', body):
                        print(f"在邮件正文中找到完全匹配的目标收件人: {target_recipient}")
                        original_recipient = target_recipient
                
                # 如果没有找到完全匹配的目标收件人，才使用正则表达式
                if not original_recipient:
                    # 匹配可能的转发邮件中的收件人格式
                    recipient_patterns = [
                        r'收件人[:：]\s*<?\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\s*>?',  # 中文格式
                        r'To[:：]\s*<?\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\s*>?',      # 英文格式
                        r'收件人[:：]\s*([^<\s\n]+)',  # 简单中文格式
                        r'To[:：]\s*([^<\s\n]+)',      # 简单英文格式
                        r'verification.*?([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',  # Cursor验证邮件特定格式
                        r'to verify.*?([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',     # 另一种常见格式
                    ]
                    
                    for pattern in recipient_patterns:
                        match = re.search(pattern, body)
                        if match:
                            found_recipient = match.group(1) if len(match.groups()) > 0 else match.group(0)
                            print(f"通过正则表达式找到可能的收件人: {found_recipient}")
                            
                            # 如果有目标收件人，只接受完全相等的匹配
                            if target_recipient:
                                if found_recipient == target_recipient:
                                    original_recipient = found_recipient
                                    print(f"找到完全匹配的目标收件人: {original_recipient}")
                                    break
                                else:
                                    print(f"找到的收件人 {found_recipient} 与目标收件人 {target_recipient} 不匹配，继续查找")
                            else:
                                original_recipient = found_recipient
                                print(f"在邮件正文中找到收件人: {original_recipient}")
                                break
                
                # 检查是否是我们要找的邮件（通过匹配收件人）
                is_target_email = False
                
                # 检查各种可能的收件人匹配 并且 Cursor登录邮件的主题特征
                if target_recipient == to_addr:
                    print(f"邮件收件人字段完全匹配目标收件人: {to_addr}")
                    is_target_email = True
                elif original_recipient and original_recipient == target_recipient:
                    print(f"邮件原始收件人完全匹配目标收件人: {original_recipient}")
                    is_target_email = True
                elif "Cursor" in subject and target_recipient in body:
                    print(f"邮件主题包含Cursor且正文包含目标收件人: {target_recipient}")
                    is_target_email = True
                
                # 无论如何，如果已经找到验证码并且收件人完全匹配，就返回验证码
                if verification_code and target_recipient == to_addr:
                    print(f"找到目标邮件，收件人完全匹配，验证码: {verification_code}")
                    pop_conn.quit()
                    return verification_code
                    
                if is_target_email and verification_code:
                    print(f"找到匹配的验证码邮件，验证码: {verification_code}")
                    pop_conn.quit()
                    return verification_code
            
            # 如果遍历完所有邮件都没找到，关闭连接
            pop_conn.quit()
            
        except Exception as e:
            print(f"检查邮件时出错: {str(e)}")
            try:
                pop_conn.quit()
            except:
                pass
        
        if attempt < max_attempts - 1:
            print(f"未找到验证码邮件，{delay}秒后重试...")
            time.sleep(delay)
    
    print("超过最大尝试次数，未找到验证码")
    return None

# 添加一个全局变量用于存储生成的密码
last_generated_password = None

def automate_website_interaction_and_get_url(email_address="",get_email_code_type=0):
    """
    自动化网站交互并获取URL
    
    参数:
    email_address: 要使用的邮箱地址，如果为空则自动生成随机邮箱
    1 表示邮箱来自 pool_a 2 表示邮箱来自 pool_b
    get_email_code_type: 获取邮箱验证码的方式，1表示2925邮箱,2表示自己邮箱，
    
    返回值:
    str: 最终的URL
    """
    global last_generated_password
    
    # 1. 使用webbrowser模块打开默认浏览器并访问网站
    url = "https://www.augmentcode.com/"
    print(f"正在打开网站: {url}")
    webbrowser.open(url)
    
    # 等待页面加载
    print("等待页面加载...")
    time.sleep(9)
    
    # 如果是Windows系统，按一次Shift键切换输入法到英文
    if sys.platform != 'darwin': # sys.platform == 'win32' for Windows
        print("在Windows上按Shift键切换到英文输入法...")
        pyautogui.press('shift')
        time.sleep(0.5) # 短暂等待确保切换生效
    
    # 确保浏览器窗口获得焦点
    print("点击浏览器窗口确保获得焦点...")
    # 点击屏幕中间位置，确保浏览器窗口获得焦点
    screen_width, screen_height = pyautogui.size()
    pyautogui.click(screen_width // 3, screen_height // 2)
    time.sleep(1)
    
    # 设置浏览器为全屏模式
    # maximize_browser_window()
    
    # 等待全屏切换完成
    time.sleep(2)
    
    print("开始执行自动化操作...")

    # singin 按钮
    pyautogui.moveTo(1128, 198, duration=0.5)
    pyautogui.click()
    print("已点击提交按钮")
    print("等待10秒...")
    time.sleep(10)

    # 有两种情况，已登陆和未登录的情况
    # 已登陆 https://app.augmentcode.com/account/subscription
    # 未登录
    current_url = get_current_url()

    if current_url.startswith("https://app.augmentcode.com/account/subscription"):
        print("当前已登陆，需要操作logout")
        print(f"点击 logout 按钮")
        pyautogui.moveTo(1245, 198, duration=0.5)
        pyautogui.click()
        time.sleep(12)
    
    # 点击 email address 输入框
    pyautogui.moveTo(638, 611, duration=0.5)
    pyautogui.click()
    time.sleep(1)
    
    send_email = email_address
    print(f"正在输入邮箱: {send_email}")
    pyautogui.write(send_email, interval=0.1)
    time.sleep(1)

    # 点击 人机校验 勾选框
    pyautogui.moveTo(624, 680, duration=0.5)
    pyautogui.click()
    time.sleep(2)
    pyautogui.click()
    print("已点击人机校验 勾选框")
    time.sleep(6)
    
    # 点击continue按钮
    pyautogui.moveTo(730, 762, duration=0.5)
    pyautogui.click()
    print("已点击提交按钮")
    print("等待10秒...")
    time.sleep(10)
    
    # 此时进入 邮箱验证码输入页面
    # https://login.augmentcode.com/u/login/passwordless-email-challenge
    # 获取当前URL
    current_url = get_current_url()
    print(f"邮箱验证码输入页面URL: {current_url}")  

    if not current_url.startswith("https://login.augmentcode.com/u/login/passwordless-email-challenge"):
        raise Exception("进入邮箱验证码输入页面失败")
        # print("进入邮箱验证码输入页面")
        # # 点击 638 572 这是人机验证勾选框的坐标，然后延迟2秒，再次获取当前URL，再次判断，如果还不是
        # pyautogui.moveTo(AUTH_CAPTCHA_CHECKBOX_X, AUTH_CAPTCHA_CHECKBOX_Y, duration=0.5)
        # pyautogui.click()
        # print("已点击人机验证勾选框")
        # time.sleep(5)
        # current_url = get_current_url()
        # print(f"当前URL: {current_url}")
        # if not current_url.startswith("https://authenticator.cursor.sh/sign-up/password?"):
        #     print("当前URL不是密码输入页面，退出")
        #     raise Exception("当前URL不是密码输入页面")
        # else:
        #     print("当前URL是密码输入页面，继续")


    print("进入邮箱验证码输入页面")
    print("正在自动获取验证码...")
    # 获取邮箱验证码 此处要判断 是哪一种方式生成的邮箱，
    # 每一种方式对应的获取验证码的方式不同
    verification_code = None
    print("开始请求邮箱验证码...")
    if get_email_code_type == 1:
        print("正在请求2925邮箱验证码...")
        verification_code = get_verification_code_smart(send_email, max_attempts=20, delay=5)
    if get_email_code_type == 2:
        print("正在请求163邮箱验证码...")
        verification_code = get_verification_code_smart(send_email, max_attempts=20, delay=5)
    if not verification_code:
        print("无法获取验证码，退出")
        raise Exception("无法获取验证码")
    
    print(f"成功获取验证码: {verification_code}")
    pyperclip.copy(verification_code)
    
    # 点击验证码输入框
    pyautogui.moveTo(657, 591, duration=0.5)
    pyautogui.click()
    time.sleep(1)
    
    # 粘贴验证码
    print("正在粘贴验证码...")
    pyautogui.hotkey(MODIFIER, 'v')
    print("延迟3秒...")
    time.sleep(1)
    
    # 点击提交按钮
    pyautogui.moveTo(732, 665, duration=0.5)
    pyautogui.click()
    print("已点击提交按钮")
    print("等待10秒...")
    time.sleep(10)
    

    after_click_send_email_code_url = get_current_url()
    print(f"点击提交后URL: {after_click_send_email_code_url}")
    # 根据当前URL 判断是否出现人机验证或是否发送code成功
    # 如果当前链接不是以这个开头，https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri
    # 那么就 点击 638 572 这是人机验证勾选框的坐标，然后延迟2秒，再次获取当前URL，再次判断，如果还不是
    # 就表示发送code失败，退出。
    if not after_click_send_email_code_url.startswith("https://auth.augmentcode.com/terms-accept?response_type=code&client_id=customer-ui&redirect_uri"):
        raise Exception("输入邮箱验证码后 点击 continue 按钮后 没有跳转到目标url")
        # pyautogui.moveTo(AUTH_CAPTCHA_CHECKBOX_X, AUTH_CAPTCHA_CHECKBOX_Y, duration=0.5)
        # pyautogui.click()
        # print("已点击人机验证勾选框")
        # time.sleep(5)
        # after_click_send_email_code_url = get_current_url()
        # print(f"点击发送邮箱验证码后URL: {after_click_send_email_code_url}")
        # if not after_click_send_email_code_url.startswith("https://authenticator.cursor.sh/email-verification?email"):
        #     print("发送code失败，退出")
        #     raise Exception("发送code失败")
    
    # 点击 I agree 框
    pyautogui.moveTo(523, 556, duration=0.5)
    pyautogui.click()
    time.sleep(1)

    # 点击 sinup and start coding 按钮
    print(f"点击 sinup and start coding 按钮")
    pyautogui.moveTo(751, 602, duration=0.5)
    pyautogui.click()
    time.sleep(15)

    # 查看url 是不是 https://app.augmentcode.com/account/subscription
    after_click_send_email_code_url = get_current_url()
    if not after_click_send_email_code_url.startswith("https://app.augmentcode.com/account/subscription"):
        raise Exception("注册失败")
        # pyautogui.moveTo(AUTH_CAPTCHA_CHECKBOX_X, AUTH_CAPTCHA_CHECKBOX_Y, duration=0.5)
        # pyautogui.click()
        # print("已点击人机验证勾选框")
        # time.sleep(5)
        # after_click_send_email_code_url = get_current_url()
        # print(f"点击发送邮箱验证码后URL: {after_click_send_email_code_url}")
        # if not after_click_send_email_code_url.startswith("https://authenticator.cursor.sh/email-verification?email"):
        #     print("发送code失败，退出")
        #     raise Exception("发送code失败")

    print("注册成功")
    time.sleep(4)

    print("准备 改变计划")
    pyautogui.moveTo(1221, 354, duration=0.5)
    pyautogui.click()
    print("已点击 upgrade plan 按钮")
    time.sleep(2)

    # 点击 Community Plan
    print(f"Community Plan")
    pyautogui.moveTo(829, 317, duration=0.5)
    pyautogui.click()
    print("已点击 Community Plan")
    time.sleep(1)

    # 点击 同意
    pyautogui.moveTo(492, 596, duration=0.5)
    pyautogui.click()
    print("已点击 同意 勾选")
    time.sleep(1)

    # 点击 select plan
    pyautogui.moveTo(989, 729, duration=0.5)
    pyautogui.click()
    print("已点击 select plan")
    time.sleep(12)
    

    # 获取最终URL
    final_url = get_current_url()
    
    # 退出全屏模式
    print("退出全屏模式...")
    if sys.platform == 'darwin':  # macOS
        # 使用Command+Control+F退出全屏
        # pyautogui.hotkey('command', 'ctrl', 'f')
    else:
        # Windows 最小化
        pyautogui.hotkey('alt', 'space')
        pyautogui.press('n')
    time.sleep(1)
    
    # 记录使用的邮箱（可选，对于随机邮箱，由于重复率低，可以选择不记录）
    try:
        save_account(send_email)
    except Exception as e:
        print(f"记录邮箱时出错: {str(e)}")
    
    print("已成功完成所有操作")
    
    return final_url



def save_account(email):
    """
    保存email到 account.csv文件里面

    参数:
    email: 要保存的邮箱地址

    返回值:
    bool: 保存是否成功
    """
    import csv
    import os
    from datetime import datetime

    try:
        csv_file = "account.csv"

        # 检查文件是否存在，如果不存在则创建并写入表头
        file_exists = os.path.exists(csv_file)

        with open(csv_file, 'a', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)

            # 如果文件不存在或为空，写入表头
            if not file_exists or os.path.getsize(csv_file) == 0:
                writer.writerow(['email', 'created_time'])

            # 写入邮箱和当前时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            writer.writerow([email, current_time])

        print(f"成功保存邮箱 {email} 到 {csv_file}")
        return True

    except Exception as e:
        print(f"保存邮箱到CSV文件时出错: {str(e)}")
        return False
def get_cursor_position():
    """获取当前鼠标位置，用于调试"""
    current_pos = pyautogui.position()
    print(f"当前鼠标位置: X={current_pos.x}, Y={current_pos.y}")
    return current_pos





def clean_verification_code(code_with_spaces):
    """
    清理带空格的验证码字符串，返回纯数字
    
    参数:
    code_with_spaces: 带有空格的验证码字符串
    
    返回值:
    str: 不含空格的纯数字验证码
    """
    # 移除所有非数字字符
    return ''.join(filter(str.isdigit, code_with_spaces))


def get_cursor_auth_from_sqlite(db_path = None):
    """
    从SQLite数据库中获取Cursor身份验证相关信息
    
    参数:
    db_path: SQLite数据库文件路径
    
    返回值:
    dict: 包含认证信息的字典，如果出错则返回空字典
    """
    auth_info = {
        'cachedEmail': None,
        'accessToken': None,
        'refreshToken': None,
        'password': None
    }
    
    # 如果未指定路径，从当前PC配置获取
    if db_path is None:
        db_path = current_pc.get_path("CURSOR_DB_PATH")
    
    try:
        print(f"尝试从SQLite数据库获取Cursor认证信息: {db_path}")
        # 连接到SQLite数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 首先获取数据库中的所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"找到数据库表: {[table[0] for table in tables]}")
        
        found_data = False
        
        # 遍历所有表，寻找可能包含认证信息的表
        for table in tables:
            table_name = table[0]
            print(f"检查表: {table_name}")
            
            # 获取表的列信息
            try:
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]
                print(f"表 {table_name} 的列: {column_names}")
                
                # 检查并查询关键字段
                if "key" in column_names and "value" in column_names:
                    print(f"在表 {table_name} 中查找认证信息...")
                    cursor.execute(f"SELECT key, value FROM {table_name} WHERE key LIKE 'cursorAuth/%'")
                    rows = cursor.fetchall()
                    if rows:
                        print(f"在表 {table_name} 中找到 {len(rows)} 行与cursorAuth相关的数据")
                        found_data = True
                        
                        # 处理查询结果
                        for key, value in rows:
                            if key == "cursorAuth/cachedEmail":
                                auth_info['cachedEmail'] = value
                                print(f"获取到缓存邮箱: {value}")
                            elif key == "cursorAuth/accessToken":
                                auth_info['accessToken'] = value
                                print(f"获取到访问令牌: {value[:10]}..." if value else "访问令牌为空")
                            elif key == "cursorAuth/refreshToken":
                                auth_info['refreshToken'] = value
                                print(f"获取到刷新令牌: {value[:10]}..." if value else "刷新令牌为空")
                            elif key == "cursorAuth/password":
                                auth_info['password'] = value
                                print(f"获取到密码: {value[:10]}..." if value else "密码为空")
                        break
                # 如果没有key和value列，尝试查找具有类似认证字段名的列
                else:
                    auth_columns = [col for col in column_names if "token" in col.lower() or "auth" in col.lower() or "email" in col.lower()]
                    if auth_columns:
                        print(f"在表 {table_name} 中发现可能的认证相关列: {auth_columns}")
                        # 对每个可能的认证列执行查询
                        cursor.execute(f"SELECT {', '.join(auth_columns)} FROM {table_name} LIMIT 5")
                        auth_data = cursor.fetchall()
                        print(f"认证数据预览: {auth_data}")
            except sqlite3.Error as e:
                print(f"查询表 {table_name} 结构时出错: {str(e)}")
        
        # 如果以上方法未找到认证信息，尝试使用SQLite的全文搜索
        if not found_data:
            print("未找到标准认证表结构，尝试在所有表内容中搜索关键字...")
            for table in tables:
                table_name = table[0]
                try:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 1")
                    sample_row = cursor.fetchone()
                    if sample_row:
                        # 检查每个列的内容是否包含认证相关关键字
                        for i, value in enumerate(sample_row):
                            if value and isinstance(value, str) and ("token" in value.lower() or "cursor" in value.lower()):
                                print(f"在表 {table_name} 的第 {i} 列发现潜在的认证信息: {value[:20]}...")
                except sqlite3.Error as e:
                    print(f"全文搜索表 {table_name} 时出错: {str(e)}")
        
        conn.close()
        
        if auth_info['cachedEmail'] or auth_info['accessToken'] or auth_info['refreshToken'] or auth_info['password']:
            print("成功获取部分或全部认证信息")
        else:
            print("未找到任何认证信息")
            
        return auth_info
        
    except sqlite3.Error as e:
        print(f"访问SQLite数据库时出错: {str(e)}")
        return auth_info
    except Exception as e:
        print(f"获取认证信息时出现异常: {str(e)}")
        return auth_info

def clear_cursor_auth_in_sqlite(db_path=None):
    """
    清空SQLite数据库中的Cursor认证信息字段
    
    参数:
    db_path: SQLite数据库文件路径
    
    返回值:
    bool: 是否成功清空认证信息
    """
    # 如果未指定路径，从当前PC配置获取
    if db_path is None:
        db_path = current_pc.get_path("CURSOR_DB_PATH")
    
    try:
        print(f"尝试清空SQLite数据库中的Cursor认证信息: {db_path}")
        # 连接到SQLite数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 首先获取数据库中的所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"找到数据库表: {[table[0] for table in tables]}")
        
        found_auth_table = False
        
        # 遍历所有表，寻找包含认证信息的表
        for table in tables:
            table_name = table[0]
            print(f"检查表: {table_name}")
            
            # 获取表的列信息
            try:
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]
                print(f"表 {table_name} 的列: {column_names}")
                
                # 检查是否是认证信息表
                if "key" in column_names and "value" in column_names:
                    # 检查表中是否包含认证信息
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE key LIKE 'cursorAuth/%'")
                    count = cursor.fetchone()[0]
                    if count > 0:
                        print(f"在表 {table_name} 中找到 {count} 行认证信息，开始清空...")
                        found_auth_table = True
                        
                        # 清空指定的认证字段
                        auth_keys = [
                            "cursorAuth/cachedEmail",
                            "cursorAuth/accessToken",
                            "cursorAuth/refreshToken"
                        ]
                        
                        for key in auth_keys:
                            # 将字段值设置为空字符串
                            cursor.execute(f"UPDATE {table_name} SET value = '' WHERE key = ?", (key,))
                            print(f"已清空字段: {key}")
                        
                        # 提交更改
                        conn.commit()
                        print(f"成功清空表 {table_name} 中的认证信息")
                        break
            except sqlite3.Error as e:
                print(f"操作表 {table_name} 时出错: {str(e)}")
        
        # 关闭连接
        conn.close()
        
        if found_auth_table:
            print("成功清空Cursor认证信息")
            return True
        else:
            print("未找到包含认证信息的表，无需清空")
            return False
            
    except sqlite3.Error as e:
        print(f"数据库操作错误: {str(e)}")
        return False
    except Exception as e:
        print(f"清空认证信息时出现异常: {str(e)}")
        return False

def save_cursor_auth_to_sqlite(auth_info, db_path=None):
    """
    将Cursor认证信息保存到SQLite数据库中
    
    参数:
    auth_info: 包含认证信息的字典，至少应包含cachedEmail、accessToken、refreshToken
    db_path: 目标SQLite数据库文件路径
    
    返回值:
    bool: 是否成功保存
    """
    # 如果未指定路径，从当前PC配置获取
    if db_path is None:
        db_path = current_pc.get_path("AUTH_DB_PATH")
    
    try:
        print(f"正在将认证信息保存到数据库: {db_path}")
        
        # 获取当前时间，格式化为YYYY-MM-DD HH:MM:SS
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 连接到SQLite数据库（如果不存在则创建）
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建表（如果不存在）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS cursor_auth (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT,
            is_upload INTEGER,
            access_token TEXT,
            refresh_token TEXT,
            password TEXT,
            created_at TEXT,
            updated_at TEXT,
            machine_id TEXT,                    /* machineId */
            telemetry_machine_id TEXT,          /* telemetry.machineId */
            env_machine_id TEXT,                /* environment.machineId */
            anonymized_id TEXT,                 /* anonymizedId */
            session_id TEXT,                    /* sessionId */
            agent_id TEXT,                      /* agentId */
            telemetry_anon_id TEXT,             /* telemetry.anon_id */
            telemetry_machine_id_hash TEXT,     /* telemetry.machineId_hash */
            telemetry_session_id TEXT,          /* telemetry.session_id */
            mac_machine_id TEXT,                /* telemetry.macMachineId */
            installation_id TEXT,               /* telemetry.installation_id */
            storage_service_machine_id TEXT,    /* storage.serviceMachineId */
            telemetry_sqm_id TEXT,              /* telemetry.sqmId */
            telemetry_dev_device_id TEXT        /* telemetry.devDeviceId */
        )
        ''')
        
        # 检查此邮箱是否已经存在记录
        cursor.execute("SELECT id FROM cursor_auth WHERE email = ?", (auth_info.get('cachedEmail'),))
        existing_record = cursor.fetchone()
        
        if existing_record:
            # 更新现有记录
            record_id = existing_record[0]
            print(f"更新现有认证记录 ID: {record_id}")
            cursor.execute('''
            UPDATE cursor_auth
            SET access_token = ?,
                refresh_token = ?,
                password = ?,
                is_upload = ?,
                updated_at = ?,
                machine_id = ?,
                telemetry_machine_id = ?,
                env_machine_id = ?,
                anonymized_id = ?,
                session_id = ?,
                agent_id = ?,
                telemetry_anon_id = ?,
                telemetry_machine_id_hash = ?,
                telemetry_session_id = ?,
                mac_machine_id = ?,
                installation_id = ?,
                storage_service_machine_id = ?,
                telemetry_sqm_id = ?,
                telemetry_dev_device_id = ?
            WHERE id = ?
            ''', (
                auth_info.get('accessToken'),
                auth_info.get('refreshToken'),
                auth_info.get('password', ''),
                0,
                current_time,
                auth_info.get('machineId', ''),
                auth_info.get('telemetry.machineId', ''),
                auth_info.get('environment.machineId', ''),
                auth_info.get('anonymizedId', ''),
                auth_info.get('sessionId', ''),
                auth_info.get('agentId', ''),
                auth_info.get('telemetry.anon_id', ''),
                auth_info.get('telemetry.machineId_hash', ''),
                auth_info.get('telemetry.session_id', ''),
                auth_info.get('telemetry.macMachineId', ''),
                auth_info.get('telemetry.installation_id', ''),
                auth_info.get('storage.serviceMachineId', ''),
                auth_info.get('telemetry.sqmId', ''),
                auth_info.get('telemetry.devDeviceId', ''),
                record_id
            ))
        else:
            # 插入新记录
            print("创建新的认证记录")
            cursor.execute('''
            INSERT INTO cursor_auth (
                email, is_upload, access_token, refresh_token, password, 
                created_at, updated_at, 
                machine_id, telemetry_machine_id, env_machine_id,
                anonymized_id, session_id, agent_id,
                telemetry_anon_id, telemetry_machine_id_hash, telemetry_session_id,
                mac_machine_id, installation_id, storage_service_machine_id, 
                telemetry_sqm_id, telemetry_dev_device_id
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                auth_info.get('cachedEmail'),
                0,  # 默认设置is_upload为0
                auth_info.get('accessToken'),
                auth_info.get('refreshToken'),
                auth_info.get('password', ''),
                current_time,
                current_time,
                auth_info.get('machineId', ''),
                auth_info.get('telemetry.machineId', ''),
                auth_info.get('environment.machineId', ''),
                auth_info.get('anonymizedId', ''),
                auth_info.get('sessionId', ''),
                auth_info.get('agentId', ''),
                auth_info.get('telemetry.anon_id', ''),
                auth_info.get('telemetry.machineId_hash', ''),
                auth_info.get('telemetry.session_id', ''),
                auth_info.get('telemetry.macMachineId', ''),
                auth_info.get('telemetry.installation_id', ''),
                auth_info.get('storage.serviceMachineId', ''),
                auth_info.get('telemetry.sqmId', ''),
                auth_info.get('telemetry.devDeviceId', '')
            ))
        
        # 提交事务
        conn.commit()
        
        # 显示更新后的记录
        cursor.execute("SELECT * FROM cursor_auth")
        records = cursor.fetchall()
        print(f"数据库中现有 {len(records)} 条认证记录:")     
        conn.close()
        print("认证信息已成功保存")
        return True
        
    except sqlite3.Error as e:
        print(f"数据库操作错误: {str(e)}")
        return False
    except Exception as e:
        print(f"保存认证信息时出现异常: {str(e)}")
        return False

def store_cursor_auth(skip_reset=False):
    """
    获取并存储Cursor认证信息和机器ID信息
    
    参数:
    skip_reset: 是否跳过机器ID重置步骤，默认为False（已被全局变量替代）
    """
    global last_generated_password
    global MACHINE_IDS_DATA
    
    machine_ids = {}
    
    # 不再重置机器ID，直接使用全局变量中的数据
    if MACHINE_IDS_DATA:
        print(f"使用已生成的机器ID数据（{len(MACHINE_IDS_DATA)}个ID）")
        machine_ids = MACHINE_IDS_DATA
    else:
        print("全局变量中没有机器ID数据，尝试其他方式获取")
        try:
            # 尝试从resetter的路径读取现有机器ID
            resetter = record_some.MachineIDResetter()
            if os.path.exists(resetter.db_path):
                with open(resetter.db_path, "r", encoding="utf-8") as f:
                    machine_ids = json.load(f)
                print(f"从现有文件获取到 {len(machine_ids)} 个ID")
        except Exception as e:
            print(f"读取现有机器ID时出错: {str(e)}")
    
    print("第2步: 获取Cursor认证信息")
    # 获取认证信息
    auth_info = get_cursor_auth_from_sqlite()
    
    # 将生成的密码添加到认证信息中
    if last_generated_password:
        auth_info['password'] = last_generated_password
        print(f"将生成的密码添加到认证信息: {last_generated_password}")
    
    # 第3步: 合并机器ID信息到auth_info
    if machine_ids:
        print("正在合并机器ID信息到认证信息...")
        # 定义ID字段的映射关系
        id_fields = [
            "machineId", 
            "telemetry.machineId", 
            "environment.machineId",
            "anonymizedId", 
            "sessionId", 
            "agentId",
            "telemetry.anon_id", 
            "telemetry.machineId_hash", 
            "telemetry.session_id",
            "telemetry.macMachineId", 
            "telemetry.installation_id",
            "storage.serviceMachineId",
            "telemetry.sqmId",
            "telemetry.devDeviceId"
        ]
        
        # 添加到认证信息中
        for field in id_fields:
            if field in machine_ids:
                auth_info[field] = machine_ids[field]
                print(f"添加 {field}: {machine_ids[field][:10]}..." if len(str(machine_ids[field])) > 10 else machine_ids[field])
    else:
        # 如果没有获取到新生成的机器ID，尝试从cursor-app.json读取
        try:
            # 尝试读取Cursor的cursor-app.json文件获取机器ID信息
            cursor_app_json_path = os.path.join(os.path.expanduser("~"), ".cursor", "cursor-app.json")
            if os.path.exists(cursor_app_json_path):
                print(f"从 {cursor_app_json_path} 读取机器ID信息")
                with open(cursor_app_json_path, "r", encoding="utf-8") as f:
                    cursor_app_data = json.load(f)
                    
                    # 提取所有机器ID相关字段
                    id_fields = [
                        "machineId", 
                        "telemetry.machineId", 
                        "environment.machineId",
                        "anonymizedId", 
                        "sessionId", 
                        "agentId",
                        "telemetry.anon_id", 
                        "telemetry.machineId_hash", 
                        "telemetry.session_id",
                        "telemetry.macMachineId", 
                        "telemetry.installation_id",
                        "storage.serviceMachineId",
                        "telemetry.sqmId",
                        "telemetry.devDeviceId"
                    ]
                    
                    # 添加到认证信息中
                    for field in id_fields:
                        if field in cursor_app_data:
                            auth_info[field] = cursor_app_data[field]
                            print(f"提取到 {field}: {cursor_app_data[field][:10]}..." if len(str(cursor_app_data[field])) > 10 else cursor_app_data[field])
            else:
                print(f"未找到机器ID文件: {cursor_app_json_path}")
        except Exception as e:
            print(f"获取机器ID信息时出错: {str(e)}")
    
    # 如果找到了认证信息，则保存到数据库
    if auth_info.get('cachedEmail') or auth_info.get('accessToken') or auth_info.get('refreshToken') or auth_info.get('password') or auth_info.get('machineId'):
        # 保存到自定义SQLite数据库
        save_cursor_auth_to_sqlite(auth_info)
        print("认证信息和机器ID处理完成")
    else:
        print("未找到足够的信息，无法保存")
    
    return auth_info

def save_email_record(email, db_path=None):
    """
    保存使用过的邮箱记录到数据库
    
    参数:
    email: 使用的邮箱地址
    db_path: 数据库文件路径
    
    返回值:
    bool: 是否保存成功
    """
    # 如果未指定路径，从当前PC配置获取
    if db_path is None:
        db_path = current_pc.get_path("EMAIL_DB_PATH")
    
    try:
        # 获取当前时间
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 连接到SQLite数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建表（如果不存在）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS email_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT NOT NULL,
            used_at TEXT NOT NULL,
            email_index INTEGER
        )
        ''')
        
        # 提取邮箱中的索引 - 支持所有配置域名
        email_index = 0
        # 构建能匹配所有域名的正则表达式
        domains_pattern = "|".join([re.escape(domain) for domain in get_all_email_domains()])
        match = re.search(rf'mail(\d+)@({domains_pattern})', email)
        if match:
            email_index = int(match.group(1))
        
        # 插入新记录
        cursor.execute('''
        INSERT INTO email_records (email, used_at, email_index)
        VALUES (?, ?, ?)
        ''', (email, current_time, email_index))
        
        # 提交事务
        conn.commit()
        
        # 打印确认信息
        print(f"已保存邮箱记录: {email}, 时间: {current_time}, 索引: {email_index}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"保存邮箱记录时出错: {str(e)}")
        return False



def generate_random_email_prefix(length=12):
    """
    生成随机邮箱前缀
    
    参数:
    length: 前缀长度，默认为12位
    
    返回值:
    str: 随机生成的邮箱前缀
    """
    # 字符集: 小写字母+数字
    chars =  string.ascii_lowercase + string.digits 
    
    # 生成随机字符串
    prefix = ''.join(random.choice(chars) for _ in range(length))
    
    return prefix

def get_random_email_domain():
    """
    从可用的邮箱域名列表中随机选择一个
    
    返回值:
    str: 随机选择的邮箱域名
    """
    # 使用全局配置的域名列表
    domains = get_all_email_domains()
    
    # 随机选择一个域名
    domain = random.choice(domains)
    
    return domain

def get_balanced_email_domain():
    """
    使用轮询机制从可用的邮箱域名列表中依次选择，确保均衡使用
    
    返回值:
    str: 均衡选择的邮箱域名
    """
    # 使用全局配置的域名列表
    domains = get_all_email_domains()
    
    # 使用一个文件来存储上次使用的域名索引
    domain_index_file = "domain_index.txt"
    
    try:
        # 尝试读取上次使用的索引
        if os.path.exists(domain_index_file):
            with open(domain_index_file, "r") as f:
                last_index = int(f.read().strip())
        else:
            last_index = -1
        
        # 计算下一个索引（循环使用）
        next_index = (last_index + 1) % len(domains)
        
        # 保存当前索引以便下次使用
        with open(domain_index_file, "w") as f:
            f.write(str(next_index))
        
        # 返回选中的域名
        return domains[next_index]
    except Exception as e:
        print(f"域名轮询出错: {str(e)}，使用随机选择作为后备方案")
        # 出错则退回到随机选择
        return random.choice(domains)

def generate_random_email(prefix_length=12, strict_balance=True):
    """
    生成完全随机的邮箱地址，包括随机前缀和随机域名
    
    参数:
    prefix_length: 邮箱前缀长度，默认为12位
    strict_balance: 是否使用严格的域名均衡机制
    
    返回值:
    str: 随机生成的完整邮箱地址
    """
    # 生成随机前缀
    prefix = generate_random_email_prefix(prefix_length)
    
    # 获取域名（根据是否需要严格均衡选择不同方法）
    if strict_balance:
        domain = get_balanced_email_domain()
    else:
        domain = get_random_email_domain()
    
    # 组合成完整邮箱
    email = f"{prefix}@{domain}"
    
    print(f"生成{'均衡' if strict_balance else '随机'}邮箱: {email}")
    return email

def generate_2925_random_email(prefix_length=10):
    """
    生成2925邮箱地址
    参数:
    prefix_length: 邮箱前缀长度，默认为10位
    strict_balance: 是否使用严格的域名均衡机制
    
    返回值:
    str: 随机生成的完整邮箱地址  <EMAIL>
    """
    prefix1 = "mailtosun"
    # 生成随机前缀
    prefix = generate_random_email_prefix(prefix_length)
    # 组合成完整邮箱
    email = f"{prefix1}{prefix}@2925.com"
    return email

def setup_logging(log_directory=None, max_size_mb=20):
    """
    设置日志记录
    
    参数:
    log_directory: 日志文件保存目录
    max_size_mb: 每个日志文件的最大大小(MB)
    
    返回值:
    logging.Logger: 配置好的日志记录器
    """
    # 如果未指定目录，从当前PC配置获取
    if log_directory is None:
        log_directory = current_pc.get_path("LOG_DIRECTORY")
    
    # 创建日志目录(如果不存在)
    if not os.path.exists(log_directory):
        os.makedirs(log_directory)
    
    # 创建日志记录器
    logger = logging.getLogger("cursor_automation")
    logger.setLevel(logging.DEBUG)
    
    # 清除已有的处理器
    if logger.handlers:
        logger.handlers.clear()
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建文件处理器，支持日志滚动
    log_file = os.path.join(log_directory, "cursor_automation.log")
    file_handler = logging.handlers.RotatingFileHandler(
        log_file, 
        maxBytes=max_size_mb * 1024 * 1024,  # 转换为字节
        backupCount=10,  # 保留的日志文件数量
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    return logger

def generate_random_password(length=10):
    """
    生成随机密码
    
    参数:
    length: 密码长度，默认为10位
    
    返回值:
    str: 随机生成的密码，包含字母、数字和下划线
    """
    # 字符集: 小写字母+大写字母+数字+下划线
    chars = string.ascii_lowercase + string.ascii_uppercase + string.digits + '_'
    
    # 生成随机字符串
    password = ''.join(random.choice(chars) for _ in range(length))
    
    # 确保密码至少包含一个数字和一个字母
    has_digit = any(c.isdigit() for c in password)
    has_alpha = any(c.isalpha() for c in password)
    
    # 如果不满足条件，重新生成
    if not (has_digit and has_alpha):
        return generate_random_password(length)
    
    return password

def get_verification_code(target_email, max_attempts=15, delay=5):
    """
    获取目标邮箱的验证码，支持重试机制
    
    参数:
    target_email: 目标邮箱地址
    max_attempts: 最大尝试次数，默认为10次
    delay: 每次尝试之间的延迟(秒)，默认为5秒
    
    返回:
    str: 验证码，如果未找到则抛出异常
    """
    print(f"尝试获取邮箱 {target_email} 的验证码...")
    
    for attempt in range(1, max_attempts + 1):
        print(f"第 {attempt}/{max_attempts} 次尝试获取验证码")
        
        try:
            # 尝试获取验证码
            code = get_2_code(target_email)
            
            # 检查验证码是否有效
            if code:
                print(f"成功获取到验证码: {code}")
                return code
            else:
                print(f"第 {attempt} 次尝试未获取到有效验证码")
        except Exception as e:
            print(f"获取验证码时出错: {str(e)}")
        
        # 如果不是最后一次尝试，则等待后重试
        if attempt < max_attempts:
            print(f"等待 {delay} 秒后重试...")
            time.sleep(delay)
    
    # 如果所有尝试都失败，抛出异常
    error_msg = f"无法获取邮箱 {target_email} 的验证码，已超过最大尝试次数 {max_attempts}"
    print(error_msg)
    raise Exception(error_msg)

def main(executions=1, interval=60, max_failures=3, log_max_size_mb=20):
    """
    主函数 - 支持多次执行和失败重试
    
    参数:
    executions: 执行次数
    interval: 每次执行之间的间隔(秒)
    max_failures: 最大连续失败次数，超过则停止执行
    log_max_size_mb: 日志文件最大大小(MB)
    """
    global MACHINE_IDS_DATA  # 声明使用全局变量
    
    # 设置日志记录
    logger = setup_logging(max_size_mb=log_max_size_mb)
    logger.info("=" * 60)
    logger.info(f"Cursor自动化操作脚本 - 计划执行 {executions} 次，间隔 {interval} 秒")
    logger.info("=" * 60)
    
    # 获取屏幕分辨率
    screen_width, screen_height = pyautogui.size()
    logger.info(f"屏幕分辨率: {screen_width}x{screen_height}")
    
    # 首先执行record_some.py中的机器ID重置功能
    # logger.info("首先执行record_some.py中的机器ID重置功能")
    # try:
    #     # 创建数据库连接
    #     conn = record_some.setup_database()
        
    #     # 调用record_some.py中的机器ID重置功能
    #     resetter = record_some.MachineIDResetter()
    #     reset_success = resetter.reset_machine_ids(conn)
        
    #     if reset_success:
    #         logger.info("成功重置机器ID")
    #         # 获取生成的新ID并保存到全局变量
    #         with open(resetter.db_path, "r", encoding="utf-8") as f:
    #             MACHINE_IDS_DATA = json.load(f)
    #         logger.info(f"获取到 {len(MACHINE_IDS_DATA)} 个新ID，已保存到全局变量")
    #     else:
    #         logger.error("机器ID重置失败，程序退出")
    #         # 关闭数据库连接
    #         conn.close()
    #         return  # 如果重置失败，直接退出函数，不执行后续流程
        
    #     # 关闭数据库连接
    #     conn.close()
    # except Exception as e:
    #     logger.error(f"执行record_some功能时出错: {str(e)}")
    #     return  # 如果出现异常，直接退出函数，不执行后续流程
    
    consecutive_failures = 0  # 连续失败计数
    successful_runs = 0       # 成功执行计数
    
    for run_number in range(1, executions + 1):
        # 每次执行使用新的随机邮箱，启用严格均衡模式
        # current_email = generate_random_email(strict_balance=True)
        # current_email = generate_2925_random_email()
        emails = [], 
        last_source = 0
        # 注意 这里调整delete_count=1, signup_count=1 
        getEmail = get_email_by_counts(delete_count=100, signup_count=1)
        if getEmail:  # 如果找到了邮箱
            print(f"获取到邮箱: {getEmail}")
            last_source = 1
            current_email = getEmail
        else:  # 如果没找到，email 为 None
            print("没有找到符合条件的邮箱")
            emails, last_source = generate_emails(1)
            current_email = emails[0]

        
        logger.info("=" * 40)
        logger.info(f"开始第 {run_number}/{executions} 次执行")
        logger.info(f"使用邮箱: {current_email}")
        logger.info("=" * 40)
        
        # 等待3秒后开始
        logger.info("程序将在3秒后启动...")
        for i in range(3, 0, -1):
            logger.info(f"{i}...")
            time.sleep(1)
        
        try:
            # 1. 先退出Cursor (如果已经运行)
            # logger.info("1. 关闭Cursor(如果已运行)")
            # close_cursor()
            # time.sleep(1)
            # clear_cursor_auth_in_sqlite()
            # time.sleep(1)
            # 2. 启动Cursor并最大化
            # logger.info("2. 启动Cursor并最大化")
            # if not start_cursor():
            #     logger.error("无法启动Cursor，请确认Cursor已安装")
            #     raise Exception("无法启动Cursor")
                
            # # 最大化cursor窗口
            # time.sleep(2)
            # maximize_cursor_window()

            # 3. 打开设置页面并登出
            # logger.info("3. 打开设置页面并登出")
            # time.sleep(2)
            # open_settings()
            # 点击logout按钮
            # logout()
            # time.sleep(1)
            # close_cursor()
            # time.sleep(1)
            # close_browser()
            time.sleep(1)

            # 4. 自动化网站交互
            logger.info("4. 开始网站交互流程")
            final_url = automate_website_interaction_and_get_url(current_email,last_source)
            logger.info(f"获取到最终链接地址: {final_url}")
            
            # 判断操作是否成功
            if check_url_success(final_url):
                logger.info("✅ 操作成功! URL包含cursor.com")
                logger.info("注册成功")
            else:
                logger.error("❌ 操作失败! URL不包含cursor.com")
                raise Exception("操作失败! URL不包含cursor.com")
                
            # 最小化浏览器
            logger.info("5. 最小化浏览器")
            if sys.platform == 'darwin': # macOS
                pyautogui.hotkey('command', 'm')
            time.sleep(2)

            # 6. 打开cursor进行登录
            # logger.info("6. 重新打开Cursor并登录")
            # start_cursor()
            # time.sleep(2)
            # 最大化cursor窗口
            # maximize_cursor_window()
            # open_settings()
            # time.sleep(2)
            # login()
            # time.sleep(15)
            # 最大化浏览器 
            # maximize_browser_window()
            # time.sleep(2)
            # to_confirm_login()
            # time.sleep(8)
            # close_browser()
            # time.sleep(1)
            
            # 7. 保存认证信息到数据库 (跳过机器ID重置步骤)
            # logger.info("7. 保存认证信息到数据库 (使用全局机器ID数据)")
            # auth_info = store_cursor_auth()
            
            
            # 如果所有操作成功完成
            consecutive_failures = 0  # 重置连续失败计数
            successful_runs += 1
            logger.info(f"✅ 第 {run_number} 次执行成功完成")
            # try:
            #     sync_data()
            # except Exception as e:
            #     logger.error(f"同步过程中发生未处理的异常: {e}")
            time.sleep(2)
            sync_data()
            
        except Exception as e:
            consecutive_failures += 1
            logger.error(f"❌ 第 {run_number} 次执行失败: {str(e)}")
            logger.error(f"连续失败次数: {consecutive_failures}/{max_failures}")
            
            # 如果连续失败次数达到上限，停止执行
            if consecutive_failures >= max_failures:
                logger.critical(f"达到最大连续失败次数({max_failures})，停止执行")
                break
        
        # 如果不是最后一次执行，等待指定的间隔时间
        if run_number < executions:
            logger.info(f"等待 {interval} 秒后开始下一次执行...")
            time.sleep(interval)
    
    # 执行统计
    logger.info("=" * 60)
    logger.info("执行统计:")
    logger.info(f"计划执行次数: {executions}")
    logger.info(f"实际执行次数: {run_number}")
    logger.info(f"成功次数: {successful_runs}")
    logger.info(f"失败次数: {run_number - successful_runs}")
    logger.info("=" * 60)
    logger.info("脚本执行完毕。")




# 如果直接运行此脚本，则执行主函数
if __name__ == "__main__":
    # 默认参数
    executions = 600        # 执行6次
    interval = 60        # 每次间隔200秒(3分钟)
    max_failures = 8      # 最大连续失败3次
    log_max_size_mb = 20  # 日志文件最大20MB
    
    # 从命令行参数获取配置(如果提供)
    if len(sys.argv) > 1:
        try:
            executions = int(sys.argv[1])
            if len(sys.argv) > 2:
                interval = int(sys.argv[2])
            if len(sys.argv) > 3:
                max_failures = int(sys.argv[3])
            if len(sys.argv) > 4:
                log_max_size_mb = int(sys.argv[4])
        except ValueError:
            print("参数格式错误。正确格式: python cursor_automation.py [执行次数] [间隔秒数] [最大失败次数] [日志大小MB]")
            sys.exit(1)
    
    # 执行主函数
    main(executions, interval, max_failures, log_max_size_mb)
    # test_email_matching_pop3() 
    # store_cursor_auth()





